<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>2</zoom_level>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>768</x>
      <y>12</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>756</x>
      <y>22</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>14</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>766</x>
      <y>38</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>30</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>738</x>
      <y>52</y>
      <w>66</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check config params
psUartHandle, psRs485DirPort, u16Rs485DirPin</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>44</y>
      <w>24</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointers]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>766</x>
      <y>68</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>60</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>772</x>
      <y>68</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid config]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>754</x>
      <y>82</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Save config params</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>74</y>
      <w>22</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid config]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>98</y>
      <w>44</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Initialize handle members</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>90</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>108</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>754</x>
      <y>116</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Clear buffers</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>744</x>
      <y>132</y>
      <w>52</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Enable UART interrupts
UART_IT_IDLE and UART_IT_ERR</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>124</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>754</x>
      <y>148</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set initialized flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>140</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>754</x>
      <y>164</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>156</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>768</x>
      <y>188</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>170</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>732</x>
      <y>0</y>
      <w>112</w>
      <h>216</h>
    </coordinates>
    <panel_attributes>HalWireComm Init
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>886</x>
      <y>12</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>874</x>
      <y>22</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>14</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>884</x>
      <y>38</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>30</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>890</x>
      <y>38</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>884</x>
      <y>68</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>44</y>
      <w>22</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>82</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Stop DMA transmission</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>74</y>
      <w>18</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>866</x>
      <y>98</y>
      <w>46</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Disable UART interrupts
UART_IT_IDLE and UART_IT_ERR</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>90</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>114</y>
      <w>32</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Clear handle members</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>106</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>124</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>132</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Clear buffers</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>130</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>148</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Clear initialized flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>140</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>164</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>156</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>862</x>
      <y>68</y>
      <w>26</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;110.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>886</x>
      <y>188</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>170</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>852</x>
      <y>0</y>
      <w>108</w>
      <h>216</h>
    </coordinates>
    <panel_attributes>HalWireComm DeInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>994</x>
      <y>12</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>982</x>
      <y>22</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>14</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>992</x>
      <y>38</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>30</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>998</x>
      <y>38</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>54</y>
      <w>36</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set default UART handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>44</y>
      <w>22</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>980</x>
      <y>70</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set default RS485 pin</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>62</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>980</x>
      <y>86</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set default RS485 port</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>78</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>980</x>
      <y>102</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>94</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>994</x>
      <y>126</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>108</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>994</x>
      <y>108</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>968</x>
      <y>0</y>
      <w>102</w>
      <h>136</h>
    </coordinates>
    <panel_attributes>HalWireComm GetDefaultConfig
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>764</x>
      <y>240</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>752</x>
      <y>250</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>242</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>266</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>258</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>266</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>272</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>282</y>
      <w>32</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Set GPIO pin state
TX: GPIO_PIN_SET
RX: GPIO_PIN_RESET</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>302</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Direction switch delay
10us delay</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>292</y>
      <w>6</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>320</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>310</y>
      <w>6</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>764</x>
      <y>342</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>326</y>
      <w>6</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>326</y>
      <w>50</w>
      <h>12</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;230.0;40.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>730</x>
      <y>228</y>
      <w>114</w>
      <h>136</h>
    </coordinates>
    <panel_attributes>HalWireComm SetDirection
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>884</x>
      <y>240</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>250</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>242</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>882</x>
      <y>266</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>258</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>888</x>
      <y>266</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>870</x>
      <y>282</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Stop DMA transmission</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>272</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>882</x>
      <y>298</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>290</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>888</x>
      <y>298</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Stop failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>870</x>
      <y>314</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set to receive mode</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>304</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Stop success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>882</x>
      <y>330</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>322</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>888</x>
      <y>330</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Set failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>870</x>
      <y>346</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set state to RX</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>336</y>
      <w>22</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Set success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>870</x>
      <y>362</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Start DMA reception</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>354</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>882</x>
      <y>378</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>370</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>888</x>
      <y>378</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[DMA failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>870</x>
      <y>394</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>384</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[DMA success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>884</x>
      <y>418</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>400</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>884</x>
      <y>400</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>854</x>
      <y>228</y>
      <w>98</w>
      <h>196</h>
    </coordinates>
    <panel_attributes>HalWireComm Receive
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>992</x>
      <y>240</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>980</x>
      <y>250</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>242</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>990</x>
      <y>266</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>258</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>996</x>
      <y>266</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>282</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Stop DMA transmission</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>272</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>990</x>
      <y>298</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>290</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>996</x>
      <y>298</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Stop failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>314</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set to transmit mode</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>304</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Stop success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>990</x>
      <y>330</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>322</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>996</x>
      <y>330</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Set failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>346</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set state to TX</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>336</y>
      <w>22</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Set success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>362</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Start DMA transmission</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>354</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>990</x>
      <y>378</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>370</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>996</x>
      <y>378</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[DMA failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>394</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>384</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[DMA success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>992</x>
      <y>418</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>400</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>400</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>962</x>
      <y>228</y>
      <w>98</w>
      <h>196</h>
    </coordinates>
    <panel_attributes>HalWireComm Transmit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>382</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>392</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>384</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>408</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>400</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>766</x>
      <y>408</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>424</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Stop DMA operation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>414</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>440</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>432</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>766</x>
      <y>440</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[DMA stop failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>456</y>
      <w>32</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Clear flags and state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>446</y>
      <w>30</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[DMA stop success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>474</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Clear RX length</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>466</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>490</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>482</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>514</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>496</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>496</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>730</x>
      <y>370</y>
      <w>112</w>
      <h>154</h>
    </coordinates>
    <panel_attributes>HalWireComm StopTransfer
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>892</x>
      <y>452</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>880</x>
      <y>462</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>454</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>890</x>
      <y>478</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>470</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>478</y>
      <w>34</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>890</x>
      <y>528</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>484</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>528</y>
      <w>34</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Not in RX state]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>878</x>
      <y>544</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get RX data length</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>534</y>
      <w>22</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[In RX state]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>890</x>
      <y>576</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>552</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>576</y>
      <w>34</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Length = 0]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>592</y>
      <w>44</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Set RX complete flags and length
Set state is IDLE_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>582</y>
      <w>20</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Length &gt; 0]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>892</x>
      <y>620</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>602</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>480</y>
      <w>38</w>
      <h>136</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;660.0;170.0;660.0;170.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>862</x>
      <y>440</y>
      <w>84</w>
      <h>190</h>
    </coordinates>
    <panel_attributes>HalWireComm RxCompleteCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>992</x>
      <y>452</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>980</x>
      <y>462</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>454</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>990</x>
      <y>478</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>470</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>996</x>
      <y>478</y>
      <w>38</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>170.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>990</x>
      <y>528</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>484</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>996</x>
      <y>528</y>
      <w>38</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Not in TX state]</panel_attributes>
    <additional_attributes>170.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>544</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set TX complete flags
Set state is IDLE_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>534</y>
      <w>20</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[In TX state]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>992</x>
      <y>572</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>552</y>
      <w>6</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>480</y>
      <w>42</w>
      <h>86</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;410.0;190.0;410.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>962</x>
      <y>440</y>
      <w>88</w>
      <h>144</h>
    </coordinates>
    <panel_attributes>HalWireComm TxCompleteCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>764</x>
      <y>544</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>752</x>
      <y>554</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>546</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>570</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>562</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>570</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>140.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>604</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set error state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>576</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>764</x>
      <y>630</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>612</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>572</y>
      <w>36</w>
      <h>54</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;250.0;160.0;250.0;160.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>730</x>
      <y>532</y>
      <w>82</w>
      <h>108</h>
    </coordinates>
    <panel_attributes>HalWireComm ErrorOccurCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>786</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>796</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>788</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>812</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>804</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>766</x>
      <y>812</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>828</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get current state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>818</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>844</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>836</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>868</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>850</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>850</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>732</x>
      <y>774</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm GetState
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>968</x>
      <y>900</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>956</x>
      <y>910</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>968</x>
      <y>902</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>966</x>
      <y>926</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>968</x>
      <y>918</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>972</x>
      <y>926</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>954</x>
      <y>942</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get TX complete flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>968</x>
      <y>932</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>954</x>
      <y>958</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>968</x>
      <y>950</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>968</x>
      <y>982</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>968</x>
      <y>964</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>968</x>
      <y>964</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>944</x>
      <y>888</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm IsTxComplete
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>794</x>
      <y>164</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>812</x>
      <y>40</y>
      <w>6</w>
      <h>128</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;620.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>772</x>
      <y>38</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Null pointers]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>170</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>912</x>
      <y>164</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>930</x>
      <y>40</y>
      <w>6</w>
      <h>128</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;620.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>170</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>872</x>
      <y>52</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check initialized flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>862</x>
      <y>70</y>
      <w>30</w>
      <h>114</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>130.0;550.0;10.0;550.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>886</x>
      <y>60</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1038</x>
      <y>40</y>
      <w>6</w>
      <h>66</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;310.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1020</x>
      <y>102</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>808</x>
      <y>268</y>
      <w>6</w>
      <h>56</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;260.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>790</x>
      <y>320</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>928</x>
      <y>268</y>
      <w>6</w>
      <h>130</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;630.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>910</x>
      <y>394</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1036</x>
      <y>268</y>
      <w>6</w>
      <h>130</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;630.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1018</x>
      <y>394</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>806</x>
      <y>410</y>
      <w>6</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;400.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>788</x>
      <y>490</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>806</x>
      <y>814</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>788</x>
      <y>844</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>994</x>
      <y>958</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1012</x>
      <y>928</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>668</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>678</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>670</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>758</x>
      <y>694</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>686</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>694</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>746</x>
      <y>710</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get RX complete flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>700</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>746</x>
      <y>726</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>718</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>750</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>732</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>732</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>730</x>
      <y>656</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm IsRxComplete
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>786</x>
      <y>726</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>804</x>
      <y>696</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>866</x>
      <y>668</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>854</x>
      <y>678</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>866</x>
      <y>670</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>864</x>
      <y>694</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>866</x>
      <y>686</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>870</x>
      <y>694</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>852</x>
      <y>710</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get RX length</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>866</x>
      <y>700</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>852</x>
      <y>726</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>866</x>
      <y>718</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>866</x>
      <y>750</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>866</x>
      <y>732</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>866</x>
      <y>732</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>836</x>
      <y>656</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm GetRxLength
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>892</x>
      <y>726</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>910</x>
      <y>696</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>972</x>
      <y>668</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>960</x>
      <y>678</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>972</x>
      <y>670</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>970</x>
      <y>694</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>972</x>
      <y>686</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>976</x>
      <y>694</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>958</x>
      <y>710</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get TX length</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>972</x>
      <y>700</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>958</x>
      <y>726</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>972</x>
      <y>718</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>972</x>
      <y>750</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>972</x>
      <y>732</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>972</x>
      <y>732</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>942</x>
      <y>656</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm GetTxLength
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>998</x>
      <y>726</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1016</x>
      <y>696</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>868</x>
      <y>786</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>856</x>
      <y>796</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>788</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>866</x>
      <y>812</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>804</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>872</x>
      <y>812</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>854</x>
      <y>828</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set RX complete flag
Clear RX length</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>818</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>854</x>
      <y>844</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>836</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>868</x>
      <y>868</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>850</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>850</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>838</x>
      <y>774</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm ClearRxComplete
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>894</x>
      <y>844</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>912</x>
      <y>814</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>974</x>
      <y>786</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>962</x>
      <y>796</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>974</x>
      <y>788</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>972</x>
      <y>812</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>974</x>
      <y>804</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>978</x>
      <y>812</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>960</x>
      <y>828</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Set TX complete flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>974</x>
      <y>818</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>960</x>
      <y>844</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>974</x>
      <y>836</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>974</x>
      <y>868</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>974</x>
      <y>850</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>974</x>
      <y>850</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>944</x>
      <y>774</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm ClearTxComplete
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1000</x>
      <y>844</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1018</x>
      <y>814</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>878</x>
      <y>512</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check RX state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>520</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>878</x>
      <y>560</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check RX length is 0</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>568</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>978</x>
      <y>512</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check TX state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>520</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>898</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>908</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>900</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>924</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>916</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>766</x>
      <y>924</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>940</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get RX buffer pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>930</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>956</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>948</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>762</x>
      <y>980</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>962</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>962</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>732</x>
      <y>886</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm GetRxBuffer
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>788</x>
      <y>956</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>806</x>
      <y>926</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>868</x>
      <y>898</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>856</x>
      <y>908</y>
      <w>28</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>900</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>866</x>
      <y>924</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>916</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>872</x>
      <y>924</y>
      <w>46</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>854</x>
      <y>940</y>
      <w>32</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Get TX buffer pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>930</y>
      <w>24</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>854</x>
      <y>956</y>
      <w>32</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>948</y>
      <w>6</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>868</x>
      <y>980</y>
      <w>4</w>
      <h>4</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>962</y>
      <w>6</w>
      <h>22</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>868</x>
      <y>962</y>
      <w>50</w>
      <h>14</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>838</x>
      <y>886</y>
      <w>98</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>HalWireComm GetTxBuffer
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>894</x>
      <y>956</y>
      <w>40</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRECOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>912</x>
      <y>926</y>
      <w>6</w>
      <h>34</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>876</x>
      <y>494</y>
      <w>36</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Convert pointer to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>892</x>
      <y>502</y>
      <w>6</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>976</x>
      <y>494</y>
      <w>36</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Convert pointer to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>502</y>
      <w>6</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>748</x>
      <y>586</y>
      <w>36</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>Convert pointer to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>764</x>
      <y>594</y>
      <w>6</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
</diagram>
