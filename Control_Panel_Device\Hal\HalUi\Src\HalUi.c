//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file HalUi.c
*
* @brief LCD User Interface Hardware Abstraction Layer Implementation File
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "HalUi.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define HALUI_BUS_SPI_POLL_TIMEOUT_D              0x1000U                   /* SPI polling transfer timeout */
#define HALUI_LCD_RESET_DELAY_MS_D                100U                      /* LCD reset delay time (milliseconds) */
#define HALUI_LCD_WAIT_POLL_DELAY_MS_D            1U                        /* LCD wait polling delay time (milliseconds) */
#define HALUI_DEFAULT_SPI_HANDLE_D                (&hspi3)                  /* Default SPI handle */
#define HALUI_DEFAULT_LCD_CS_PIN_D                (LCD_CS_Pin)              /* Default LCD chip select pin */
#define HALUI_DEFAULT_LCD_CS_PORT_D               (LCD_CS_GPIO_Port)        /* Default LCD chip select port */
#define HALUI_DEFAULT_LCD_DCX_PIN_D               (LCD_DCX_Pin)             /* Default LCD data/command pin */
#define HALUI_DEFAULT_LCD_DCX_PORT_D              (LCD_DCX_GPIO_Port)       /* Default LCD data/command port */
#define HALUI_DEFAULT_LCD_RESET_PIN_D             (LCD_RESET_Pin)           /* Default LCD reset pin */
#define HALUI_DEFAULT_LCD_RESET_PORT_D            (LCD_RESET_GPIO_Port)     /* Default LCD reset port */
#define HALUI_DEFAULT_LCD_H_RES_D                 240U                      /* Default horizontal resolution */
#define HALUI_DEFAULT_LCD_V_RES_D                 320U                      /* Default vertical resolution */
#define HALUI_DEFAULT_COLOR_INVERT_D              TRUE_D                    /* Default color invert flag */
#define HALUI_DEFAULT_ANTI_ALIASING_D             TRUE_D                    /* Default anti-aliasing flag */
#define HALUI_DEFAULT_ROTATION_D                  LV_DISPLAY_ROTATION_270   /* Default screen rotation angle */
#define HALUI_DEFAULT_BUFFER_DIVISOR_D            10U                       /* Default buffer size divisor (1/10 of screen) */

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------
static HALUI_HANDLE_T sSingletonHandle = {0};     /* Global HalUi handle instance */

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize LCD hardware abstraction layer.
* @remark None.
*
* @param psConfig [in]: Configuration parameter pointer.
* @return Operation status.
*/
HALUI_STATUS_E HalUi_eInit(const HALUI_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return HALUI_ERROR_E;
    }
    
    /* Check configuration parameter validity */
    if ((psConfig->psSpiHandle == NULL_D) || (psConfig->psLcdCsPort == NULL_D) || (psConfig->psLcdDcxPort == NULL_D) || (psConfig->psLcdResetPort == NULL_D) ||
        (psConfig->u16LcdCsPin == 0U) || (psConfig->u16LcdDcxPin == 0U) || (psConfig->u16LcdResetPin == 0U))
    {
        return HALUI_ERROR_E;
    }

    if ((psConfig->sLcdDisplayConfig.u16HRes == 0U) || (psConfig->sLcdDisplayConfig.u16VRes == 0U))
    {
        return HALUI_ERROR_E;
    }

    if ((psConfig->sLcdDisplayConfig.u32BufferDivisor == 0U) || (psConfig->sLcdDisplayConfig.u32BufferDivisor > 100U))
    {
        return HALUI_ERROR_E;
    }
    
    /* Save configuration parameters */
    sSingletonHandle.sConfig = *psConfig;
    
    /* Initialize other handle members */
    sSingletonHandle.eLcdBusState = HALUI_LCD_BUS_IDLE_E;
    sSingletonHandle.psLcdDisplay = NULL_D;
    sSingletonHandle.psDrawBuf1 = NULL_D;
    sSingletonHandle.psDrawBuf2 = NULL_D;
    
    /* Initialize LCD IO interface */
    if (HalUi_eLcdIoInit() != HALUI_OK_E)
    {
        return HALUI_ERROR_E;
    }
    
    sSingletonHandle.bInitialized = TRUE_D;
    
    return HALUI_OK_E;
}

/**
* @brief Deinitialize LCD hardware abstraction layer.
* @remark None.
*
* @return Nothing.
*/
void HalUi_vDeInit(void)
{
    if (sSingletonHandle.bInitialized)
    {
        /* Free display buffers if allocated */
        if (sSingletonHandle.psDrawBuf1 != NULL_D)
        {
            lv_free(sSingletonHandle.psDrawBuf1);
            sSingletonHandle.psDrawBuf1 = NULL_D;
        }

        /* Free display buffers if allocated */
        if (sSingletonHandle.psDrawBuf2 != NULL_D)
        {
            lv_free(sSingletonHandle.psDrawBuf2);
            sSingletonHandle.psDrawBuf2 = NULL_D;
        }
        
        /* Clear handle */
        sSingletonHandle.sConfig = (HALUI_CONFIG_T){0};
        sSingletonHandle.eLcdBusState = HALUI_LCD_BUS_IDLE_E;
        sSingletonHandle.psLcdDisplay = NULL_D;
        sSingletonHandle.bInitialized = FALSE_D;
    }
}

/**
* @brief Get default configuration parameters.
* @remark None.
*
* @param psConfig [out]: Configuration structure pointer to return default configuration.
* @return Operation status.
*/
HALUI_STATUS_E HalUi_eGetDefaultConfig(HALUI_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return HALUI_ERROR_E;
    }
    
    /* Set default hardware configuration */
    psConfig->psSpiHandle = HALUI_DEFAULT_SPI_HANDLE_D;
    psConfig->u16LcdCsPin = HALUI_DEFAULT_LCD_CS_PIN_D;
    psConfig->psLcdCsPort = HALUI_DEFAULT_LCD_CS_PORT_D;
    psConfig->u16LcdDcxPin = HALUI_DEFAULT_LCD_DCX_PIN_D;
    psConfig->psLcdDcxPort = HALUI_DEFAULT_LCD_DCX_PORT_D;
    psConfig->u16LcdResetPin = HALUI_DEFAULT_LCD_RESET_PIN_D;
    psConfig->psLcdResetPort = HALUI_DEFAULT_LCD_RESET_PORT_D;
    
    /* Set default display configuration */
    psConfig->sLcdDisplayConfig.u16HRes = HALUI_DEFAULT_LCD_H_RES_D;
    psConfig->sLcdDisplayConfig.u16VRes = HALUI_DEFAULT_LCD_V_RES_D;
    psConfig->sLcdDisplayConfig.bColorInvert = HALUI_DEFAULT_COLOR_INVERT_D;
    psConfig->sLcdDisplayConfig.bAntiAliasing = HALUI_DEFAULT_ANTI_ALIASING_D;
    psConfig->sLcdDisplayConfig.eRotation = HALUI_DEFAULT_ROTATION_D;
    psConfig->sLcdDisplayConfig.u32BufferDivisor = HALUI_DEFAULT_BUFFER_DIVISOR_D;
    psConfig->sLcdDisplayConfig.u32DrawBufSize = 0U;
    
    return HALUI_OK_E;
}

/**
* @brief Initialize LCD IO interface.
* @remark None.
*
* @return Operation status.
*/
HALUI_STATUS_E HalUi_eLcdIoInit(void)
{    
    /* Register SPI transfer complete callback */
    if (HAL_SPI_RegisterCallback(sSingletonHandle.sConfig.psSpiHandle, HAL_SPI_TX_COMPLETE_CB_ID, HalUi_vLcdColorTransferReadyCallback) != HAL_OK)
    {
        return HALUI_ERROR_E;
    }

    /* Reset LCD */
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdResetPort, sSingletonHandle.sConfig.u16LcdResetPin, GPIO_PIN_RESET);
    osDelay(HALUI_LCD_RESET_DELAY_MS_D);
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdResetPort, sSingletonHandle.sConfig.u16LcdResetPin, GPIO_PIN_SET);
    osDelay(HALUI_LCD_RESET_DELAY_MS_D);

    /* Set CS and DCX to high level */
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdCsPort, sSingletonHandle.sConfig.u16LcdCsPin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdDcxPort, sSingletonHandle.sConfig.u16LcdDcxPin, GPIO_PIN_SET);

    return HALUI_OK_E;
}

/**
* @brief Create and configure LCD display.
* @remark None.
*
* @return Operation status.
*/
HALUI_STATUS_E HalUi_eCreateLcdDisplay(void)
{
    /* Check initialization status */
    if (!sSingletonHandle.bInitialized)
    {
        return HALUI_ERROR_E;
    }

    /* If display already created, return success */
    if (sSingletonHandle.psLcdDisplay != NULL_D)
    {
        return HALUI_OK_E;
    }
    
    /* Create ST7789 display */
    sSingletonHandle.psLcdDisplay = lv_st7789_create(sSingletonHandle.sConfig.sLcdDisplayConfig.u16HRes, 
                                                     sSingletonHandle.sConfig.sLcdDisplayConfig.u16VRes, 
                                                     LV_LCD_FLAG_NONE, 
                                                     HalUi_vLcdSendCommand, 
                                                     HalUi_vLcdSendColor);
    
    /* Check if display creation failed */
    if (sSingletonHandle.psLcdDisplay == NULL_D)
    {
        return HALUI_ERROR_E;
    }
    
    /* Set display rotation */
    lv_display_set_rotation(sSingletonHandle.psLcdDisplay, sSingletonHandle.sConfig.sLcdDisplayConfig.eRotation);
    
    /* Set color inversion */
    lv_st7789_set_invert(sSingletonHandle.psLcdDisplay, sSingletonHandle.sConfig.sLcdDisplayConfig.bColorInvert);
    
    /* Set anti-aliasing */
    lv_display_set_antialiasing(sSingletonHandle.psLcdDisplay, sSingletonHandle.sConfig.sLcdDisplayConfig.bAntiAliasing);
    
    /* Calculate and allocate display buffers */
    /* Buffer size is configurable fraction of the screen size to balance memory usage and performance */
    sSingletonHandle.sConfig.sLcdDisplayConfig.u32DrawBufSize = sSingletonHandle.sConfig.sLcdDisplayConfig.u16HRes * 
                                                                sSingletonHandle.sConfig.sLcdDisplayConfig.u16VRes / 
                                                                sSingletonHandle.sConfig.sLcdDisplayConfig.u32BufferDivisor * 
                                                                lv_color_format_get_size(lv_display_get_color_format(sSingletonHandle.psLcdDisplay));
    
    /* Allocate display buffers */
    sSingletonHandle.psDrawBuf1 = lv_malloc(sSingletonHandle.sConfig.sLcdDisplayConfig.u32DrawBufSize);
    if (sSingletonHandle.psDrawBuf1 == NULL_D)
    {
        return HALUI_ERROR_E;
    }
    
    /* Allocate display buffers */
    sSingletonHandle.psDrawBuf2 = lv_malloc(sSingletonHandle.sConfig.sLcdDisplayConfig.u32DrawBufSize);
    if (sSingletonHandle.psDrawBuf2 == NULL_D)
    {
        lv_free(sSingletonHandle.psDrawBuf1);
        sSingletonHandle.psDrawBuf1 = NULL_D;
        return HALUI_ERROR_E;
    }
    
    /* Configure display buffers */
    lv_display_set_buffers(sSingletonHandle.psLcdDisplay,
                           sSingletonHandle.psDrawBuf1, 
                           sSingletonHandle.psDrawBuf2, 
                           sSingletonHandle.sConfig.sLcdDisplayConfig.u32DrawBufSize, 
                           LV_DISPLAY_RENDER_MODE_PARTIAL);
    
    return HALUI_OK_E;
}

/**
* @brief Check if LCD bus is busy.
* @remark None.
*
* @param pbBusy [out]: Busy status pointer (TRUE_D-busy, FALSE_D-idle).
* @return Operation status.
*/
HALUI_STATUS_E HalUi_eIsLcdBusy(BOOL* const pbBusy)
{
    /* Parameter validity check */
    if ((pbBusy == NULL_D) || (!sSingletonHandle.bInitialized))
    {
        return HALUI_ERROR_E;
    }
    
    *pbBusy = (sSingletonHandle.eLcdBusState != HALUI_LCD_BUS_IDLE_E);

    return HALUI_OK_E;
}

/**
* @brief Wait for LCD bus to be idle (cannot be called in interrupt).
* @remark None.
*
* @param u32TimeoutMs [in]: Timeout time (milliseconds).
* @return Operation status.
*/
HALUI_STATUS_E HalUi_eWaitLcdReady(const U32 u32TimeoutMs)
{
    /* Check initialization status */
    if (!sSingletonHandle.bInitialized)
    {
        return HALUI_ERROR_E;
    }
    
    /* Get start tick */
    TickType_t xStartTick = xTaskGetTickCount();
    
    /* Convert timeout time to ticks */
    TickType_t xTimeoutTicks = pdMS_TO_TICKS(u32TimeoutMs);
    
    /* Wait for LCD bus to be idle */
    while (sSingletonHandle.eLcdBusState != HALUI_LCD_BUS_IDLE_E)
    {
        /* Check for timeout */
        if ((xTaskGetTickCount() - xStartTick) >= xTimeoutTicks)
        {
            return HALUI_TIMEOUT_E;
        }

        /* Delay for short time */
        osDelay(HALUI_LCD_WAIT_POLL_DELAY_MS_D);
    }
    
    return HALUI_OK_E;
}

/**
* @brief Get LCD display handle.
* @remark None.
*
* @param ppsLcdDisplay [out]: Pointer to display handle pointer.
* @return Operation status.
*/
HALUI_STATUS_E HalUi_eGetLcdDisplay(lv_display_t** const ppsLcdDisplay)
{
    /* Parameter validity check */
    if ((ppsLcdDisplay == NULL_D) || (!sSingletonHandle.bInitialized))
    {
        return HALUI_ERROR_E;
    }
    
    *ppsLcdDisplay = sSingletonHandle.psLcdDisplay;

    return HALUI_OK_E;
}

/**
* @brief LCD color transfer complete callback function (to be called in SPI interrupt service routine).
* @remark None.
*
* @param psSpiHandle [in]: SPI handle pointer.
* @return Nothing.
*/
void HalUi_vLcdColorTransferReadyCallback(SPI_HandleTypeDef* psSpiHandle)
{
    LV_UNUSED(psSpiHandle);
    
    /* Check if global handle is valid */
    if (!sSingletonHandle.bInitialized)
    {
        return;
    }
    
    /* Pull CS high */
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdCsPort, sSingletonHandle.sConfig.u16LcdCsPin, GPIO_PIN_SET);

    /* Set LCD bus state to idle */
    sSingletonHandle.eLcdBusState = HALUI_LCD_BUS_IDLE_E;
    
    /* Notify LVGL that color transfer is complete */
    if (sSingletonHandle.psLcdDisplay != NULL_D)
    {
        /* Notify LVGL that color transfer is complete */
        lv_display_flush_ready(sSingletonHandle.psLcdDisplay);
    }
}

/**
* @brief LCD send command function (LVGL callback).
* @remark None.
*
* @param psDisp [in]: Display handle pointer.
* @param pu8Cmd [in]: Command data pointer.
* @param szCmdSize [in]: Command size.
* @param pu8Param [in]: Parameter data pointer.
* @param szParamSize [in]: Parameter size.
* @return Nothing.
*/
void HalUi_vLcdSendCommand(lv_display_t *psDisp, const uint8_t* pu8Cmd, size_t szCmdSize, const uint8_t* pu8Param, size_t szParamSize)
{
    LV_UNUSED(psDisp);
    
    /* Check if global handle is valid */
    if (!sSingletonHandle.bInitialized)
    {
        return;
    }
    
    /* Wait for previous transfer to complete */
    while (sSingletonHandle.eLcdBusState != HALUI_LCD_BUS_IDLE_E);
    
    /* Set SPI to 8-bit mode */
    sSingletonHandle.sConfig.psSpiHandle->Init.DataSize = SPI_DATASIZE_8BIT;
    /* Initialize SPI */
    HAL_SPI_Init(sSingletonHandle.sConfig.psSpiHandle);
    
    /* Set DCX to low level (command) */
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdDcxPort, sSingletonHandle.sConfig.u16LcdDcxPin, GPIO_PIN_RESET);
    /* Set CS to low level */
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdCsPort, sSingletonHandle.sConfig.u16LcdCsPin, GPIO_PIN_RESET);
    
    /* Send command */
    if (HAL_SPI_Transmit(sSingletonHandle.sConfig.psSpiHandle, pu8Cmd, (uint16_t)szCmdSize, HALUI_BUS_SPI_POLL_TIMEOUT_D) == HAL_OK)
    {
        /* Set DCX to high level (data) */
        HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdDcxPort, sSingletonHandle.sConfig.u16LcdDcxPin, GPIO_PIN_SET);
        /* Use polling transfer for short data blocks */
        HAL_SPI_Transmit(sSingletonHandle.sConfig.psSpiHandle, pu8Param, (uint16_t)szParamSize, HALUI_BUS_SPI_POLL_TIMEOUT_D);
        /* Set CS to high level */
        HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdCsPort, sSingletonHandle.sConfig.u16LcdCsPin, GPIO_PIN_SET);
    }
}

/**
* @brief LCD send color data function (LVGL callback).
* @remark None.
*
* @param psDisp [in]: Display handle pointer.
* @param pu8Cmd [in]: Command data pointer.
* @param szCmdSize [in]: Command size.
* @param pu8Param [in]: Parameter data pointer.
* @param szParamSize [in]: Parameter size.
* @return Nothing.
*/
void HalUi_vLcdSendColor(lv_display_t *psDisp, const uint8_t* pu8Cmd, size_t szCmdSize, uint8_t* pu8Param, size_t szParamSize)
{
    LV_UNUSED(psDisp);
    
    /* Check if global handle is valid */
    if (!sSingletonHandle.bInitialized)
    {
        return;
    }
    
    /* Wait for previous transfer to complete */
    while (sSingletonHandle.eLcdBusState != HALUI_LCD_BUS_IDLE_E);
    
    /* Set SPI to 8-bit mode */
    sSingletonHandle.sConfig.psSpiHandle->Init.DataSize = SPI_DATASIZE_8BIT;
    /* Initialize SPI */
    HAL_SPI_Init(sSingletonHandle.sConfig.psSpiHandle);
    
    /* Set DCX to low level (command) */
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdDcxPort, sSingletonHandle.sConfig.u16LcdDcxPin, GPIO_PIN_RESET);
    /* Set CS to low level */
    HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdCsPort, sSingletonHandle.sConfig.u16LcdCsPin, GPIO_PIN_RESET);
    
    /* Send color data */
    if (HAL_SPI_Transmit(sSingletonHandle.sConfig.psSpiHandle, pu8Cmd, (uint16_t)szCmdSize, HALUI_BUS_SPI_POLL_TIMEOUT_D) == HAL_OK)
    {
        /* Set DCX to high level (data) */
        HAL_GPIO_WritePin(sSingletonHandle.sConfig.psLcdDcxPort, sSingletonHandle.sConfig.u16LcdDcxPin, GPIO_PIN_SET);
        /* Use DMA transfer for color data */
        /* Set SPI to 16-bit mode to match byte order */
        sSingletonHandle.sConfig.psSpiHandle->Init.DataSize = SPI_DATASIZE_16BIT;
        /* Initialize SPI */
        HAL_SPI_Init(sSingletonHandle.sConfig.psSpiHandle);
        /* Set LCD bus state to busy */
        sSingletonHandle.eLcdBusState = HALUI_LCD_BUS_BUSY_E;
        /* Send color data */
        HAL_SPI_Transmit_DMA(sSingletonHandle.sConfig.psSpiHandle, pu8Param, (uint16_t)szParamSize / 2U);
    }
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

//===========================================================================
// End of file.
//===========================================================================








