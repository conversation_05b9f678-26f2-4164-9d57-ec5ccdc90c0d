/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    usart.h
  * @brief   This file contains all the function prototypes for
  *          the usart.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __USART_H__
#define __USART_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

extern UART_HandleTypeDef huart1;

extern UART_HandleTypeDef huart2;

extern UART_HandleTypeDef huart3;

/* USER CODE BEGIN Private defines */
typedef enum
{
    UART_CALLBACK_IDLE_E = 0U,
    UART_CALLBACK_TX_COMPLETE_E,
    UART_CALLBACK_ERROR_E,
    UART_CALLBACK_TYPE_MAX_E
} UART_CALLBACK_TYPE_E;

typedef struct UART_CALLBACK_FUNCTION_NODE_T
{
    void (*pvUartCallbackFunc)(void *pvUserData);
    UART_HandleTypeDef *psTargetUart;
    void *pvCallbackData;
    struct UART_CALLBACK_FUNCTION_NODE_T *psNextNode;
} UART_CALLBACK_FUNCTION_NODE_T;

/* USER CODE END Private defines */

void MX_USART1_UART_Init(void);
void MX_USART2_UART_Init(void);
void MX_USART3_UART_Init(void);

/* USER CODE BEGIN Prototypes */
void UartCallbackManager_vInit(void);
void UartCallbackManager_vClearCallbacks(UART_CALLBACK_TYPE_E eCallbackType);
void UartCallbackManager_vAddIdleCallback(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData);
void UartCallbackManager_vAddTxCompleteCallback(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData);
void UartCallbackManager_vAddErrorCallback(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData);
void UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_TYPE_E eCallbackType, UART_HandleTypeDef* huart);

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __USART_H__ */

