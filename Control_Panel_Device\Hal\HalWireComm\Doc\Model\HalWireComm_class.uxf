<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>9</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>387</x>
      <y>216</y>
      <w>1161</w>
      <h>351</h>
    </coordinates>
    <panel_attributes>HalWireComm
--
+HalWireComm_eInit(psHandle:HALWIRECOMM_HANDLE_T* const, psConfig:const HALWIRECOMM_CONFIG_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eDeInit(psHandle:HALWIRECOMM_HANDLE_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eGetDefaultConfig(psConfig:HALWIRECOMM_CONFIG_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eSetDirection(psHandle:const H<PERSON><PERSON>RECOMM_HANDLE_T* const, eDirection:const HALWIRECOMM_DIRECTION_E):HALWIRECOMM_STATUS_E
+HalWireComm_eReceive(psHandle:HALWIRECOMM_HANDLE_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eTransmit(psHandle:HALWIRECOMM_HANDLE_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eStopTransfer(psHandle:HALWIRECOMM_HANDLE_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eGetState(psHandle:const HALWIRECOMM_HANDLE_T* const, peState:HALWIRECOMM_STATE_E* const):HALWIRECOMM_STATUS_E
+HalWireComm_eIsTxComplete(psHandle:const HALWIRECOMM_HANDLE_T* const, pbComplete:BOOL* const):HALWIRECOMM_STATUS_E
+HalWireComm_eIsRxComplete(psHandle:const HALWIRECOMM_HANDLE_T* const, pbComplete:BOOL* const):HALWIRECOMM_STATUS_E
+HalWireComm_eGetRxLength(psHandle:const HALWIRECOMM_HANDLE_T* const, pu16Length:U16* const):HALWIRECOMM_STATUS_E
+HalWireComm_eSetTxLength(psHandle:HALWIRECOMM_HANDLE_T* const, u16Length:const U16):HALWIRECOMM_STATUS_E
+HalWireComm_eClearRxComplete(psHandle:HALWIRECOMM_HANDLE_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eClearTxComplete(psHandle:HALWIRECOMM_HANDLE_T* const):HALWIRECOMM_STATUS_E
+HalWireComm_eGetRxBuffer(psHandle:const HALWIRECOMM_HANDLE_T* const, ppu8RxBuffer:U8** const):HALWIRECOMM_STATUS_E
+HalWireComm_eGetTxBuffer(psHandle:const HALWIRECOMM_HANDLE_T* const, ppu8TxBuffer:U8** const):HALWIRECOMM_STATUS_E
+HalWireComm_vRxCompleteCallback(pvUserData:void* const):void
+HalWireComm_vTxCompleteCallback(pvUserData:void* const):void
+HalWireComm_vErrorOccurCallback(pvUserData:void* const):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>18</x>
      <y>216</y>
      <w>324</w>
      <h>81</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
HALWIRECOMM_CONFIG_T
--
+psUartHandle:UART_HandleTypeDef*
+u16Rs485DirPin:U16
+psRs485DirPort:GPIO_TypeDef*</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>0</x>
      <y>306</y>
      <w>342</w>
      <h>180</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
HALWIRECOMM_HANDLE_T
--
+sConfig:HALWIRECOMM_CONFIG_T
+eState:volatile HALWIRECOMM_STATE_E
+bTxComplete:volatile BOOL
+bRxComplete:volatile BOOL
+u16RxLength:volatile U16
+u16TxLength:volatile U16
+au8RxBuffer[HALWIRECOMM_RX_BUFFER_SIZE_D]:U8
+au8TxBuffer[HALWIRECOMM_TX_BUFFER_SIZE_D]:U8
+bInitialized:BOOL</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>531</x>
      <y>63</y>
      <w>846</w>
      <h>108</h>
    </coordinates>
    <panel_attributes>HAL_GPIO / HAL_UART
--
+HAL_GPIO_WritePin(GPIOx:GPIO_TypeDef*, GPIO_Pin:U16, PinState:GPIO_PinState):void
+HAL_GPIO_ReadPin(GPIOx:GPIO_TypeDef*, GPIO_Pin:U16):GPIO_PinState
+HAL_UARTEx_ReceiveToIdle_DMA(huart:UART_HandleTypeDef*, pData:U8*, Size:U16):HAL_StatusTypeDef
+HAL_UART_Transmit_DMA(huart:UART_HandleTypeDef*, pData:U8*, Size:U16):HAL_StatusTypeDef
+HAL_UART_DMAStop(huart:UART_HandleTypeDef*):HAL_StatusTypeDef</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>936</x>
      <y>162</y>
      <w>63</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>738</x>
      <y>612</y>
      <w>414</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>Utility
--
+Utility_vDelayUs(u32DelayUs:U32):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>936</x>
      <y>558</y>
      <w>63</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1593</x>
      <y>216</y>
      <w>243</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALWIRECOMM_STATUS_E
--
HALWIRECOMM_OK_E = 0
HALWIRECOMM_ERROR_E
HALWIRECOMM_BUSY_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1539</x>
      <y>252</y>
      <w>72</w>
      <h>27</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1593</x>
      <y>324</y>
      <w>243</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALWIRECOMM_DIRECTION_E
--
HALWIRECOMM_DIR_RX_E = 0
HALWIRECOMM_DIR_TX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1539</x>
      <y>351</y>
      <w>72</w>
      <h>27</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1593</x>
      <y>414</y>
      <w>243</w>
      <h>99</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALWIRECOMM_STATE_E
--
HALWIRECOMM_STATE_IDLE_E = 0
HALWIRECOMM_STATE_RX_E
HALWIRECOMM_STATE_TX_E
HALWIRECOMM_STATE_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1539</x>
      <y>450</y>
      <w>72</w>
      <h>27</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>333</x>
      <y>234</y>
      <w>72</w>
      <h>27</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>333</x>
      <y>414</y>
      <w>72</w>
      <h>27</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
</diagram>
