//----------------------------------------------------------------------------
/**
* @file HalWireComm.h
* @remark HalWireComm public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef HALWIRECOMM_H_
#define HALWIRECOMM_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "main.h"
#include "GlobalTypes.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------
#define HALWIRECOMM_RX_BUFFER_SIZE_D     1024U       /* Receive buffer size */
#define HALWIRECOMM_TX_BUFFER_SIZE_D     1024U       /* Transmit buffer size */

/**
* @brief RS485 communication operation status enumeration
*/
typedef enum
{
    HALWIRECOMM_OK_E = 0U,                   /* Operation successful */
    HALWIRECOMM_ERROR_E,                     /* Operation failed */
    HALWIRECOMM_BUSY_E                       /* Device busy */
} HALWIRECOMM_STATUS_E;

/**
* @brief RS485 communication direction enumeration
*/
typedef enum
{
    HALWIRECOMM_DIR_RX_E = 0U,               /* Receive direction */
    HALWIRECOMM_DIR_TX_E                     /* Transmit direction */
} HALWIRECOMM_DIRECTION_E;

/**
* @brief RS485 communication state enumeration
*/
typedef enum
{
    HALWIRECOMM_STATE_IDLE_E = 0U,           /* Idle state */
    HALWIRECOMM_STATE_RX_E,                  /* Receive state */
    HALWIRECOMM_STATE_TX_E,                  /* Transmit state */
    HALWIRECOMM_STATE_ERROR_E                /* Error state */
} HALWIRECOMM_STATE_E;

/**
* @brief RS485 communication configuration structure
* @details Contains all configuration parameters for RS485 communication
*/
typedef struct
{
    UART_HandleTypeDef* psUartHandle;       /* UART handle pointer */
    U16 u16Rs485DirPin;                     /* RS485 direction control pin */
    GPIO_TypeDef* psRs485DirPort;           /* RS485 direction control port */
} HALWIRECOMM_CONFIG_T;

/**
* @brief RS485 communication handle structure
*/
typedef struct
{
    HALWIRECOMM_CONFIG_T sConfig;                        /* Configuration parameters structure */
    volatile HALWIRECOMM_STATE_E eState;                 /* Current state */
    volatile BOOL bTxComplete;                          /* Transmit complete flag */
    volatile BOOL bRxComplete;                          /* Receive complete flag */
    volatile U16 u16RxLength;                           /* Received data length */
    volatile U16 u16TxLength;                           /* Transmitted data length */
    U8 au8RxBuffer[HALWIRECOMM_RX_BUFFER_SIZE_D];        /* Receive buffer */
    U8 au8TxBuffer[HALWIRECOMM_TX_BUFFER_SIZE_D];        /* Transmit buffer */
    BOOL bInitialized;                                  /* Initialization flag */
} HALWIRECOMM_HANDLE_T;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
HALWIRECOMM_STATUS_E HalWireComm_eInit(HALWIRECOMM_HANDLE_T* const psHandle, const HALWIRECOMM_CONFIG_T* const psConfig);
HALWIRECOMM_STATUS_E HalWireComm_eDeInit(HALWIRECOMM_HANDLE_T* const psHandle);
HALWIRECOMM_STATUS_E HalWireComm_eGetDefaultConfig(HALWIRECOMM_CONFIG_T* const psConfig);
HALWIRECOMM_STATUS_E HalWireComm_eSetDirection(const HALWIRECOMM_HANDLE_T* const psHandle, const HALWIRECOMM_DIRECTION_E eDirection);
HALWIRECOMM_STATUS_E HalWireComm_eReceive(HALWIRECOMM_HANDLE_T* const psHandle);
HALWIRECOMM_STATUS_E HalWireComm_eTransmit(HALWIRECOMM_HANDLE_T* const psHandle);
HALWIRECOMM_STATUS_E HalWireComm_eStopTransfer(HALWIRECOMM_HANDLE_T* const psHandle);
HALWIRECOMM_STATUS_E HalWireComm_eGetState(const HALWIRECOMM_HANDLE_T* const psHandle, HALWIRECOMM_STATE_E* const peState);
HALWIRECOMM_STATUS_E HalWireComm_eIsTxComplete(const HALWIRECOMM_HANDLE_T* const psHandle, BOOL* const pbComplete);
HALWIRECOMM_STATUS_E HalWireComm_eIsRxComplete(const HALWIRECOMM_HANDLE_T* const psHandle, BOOL* const pbComplete);
HALWIRECOMM_STATUS_E HalWireComm_eGetRxLength(const HALWIRECOMM_HANDLE_T* const psHandle, U16* const pu16Length);
HALWIRECOMM_STATUS_E HalWireComm_eSetTxLength(HALWIRECOMM_HANDLE_T* const psHandle, const U16 u16Length);
HALWIRECOMM_STATUS_E HalWireComm_eGetRxBuffer(const HALWIRECOMM_HANDLE_T* const psHandle, U8** const ppu8RxBuffer);
HALWIRECOMM_STATUS_E HalWireComm_eGetTxBuffer(const HALWIRECOMM_HANDLE_T* const psHandle, U8** const ppu8TxBuffer);
HALWIRECOMM_STATUS_E HalWireComm_eClearRxComplete(HALWIRECOMM_HANDLE_T* const psHandle);
HALWIRECOMM_STATUS_E HalWireComm_eClearTxComplete(HALWIRECOMM_HANDLE_T* const psHandle);
void HalWireComm_vRxCompleteCallback(void* const pvUserData);
void HalWireComm_vTxCompleteCallback(void* const pvUserData);
void HalWireComm_vErrorOccurCallback(void* const pvUserData);

#endif /* HALWIRECOMM_H_ */

//===========================================================================
// End of file.
//===========================================================================








