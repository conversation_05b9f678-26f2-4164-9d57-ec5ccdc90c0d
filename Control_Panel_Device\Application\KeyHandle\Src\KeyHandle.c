//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file KeyHandle.c
*
* @brief Key handling module implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "KeyHandle.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define KEYHANDLE_SINGLE_KEY_DEBOUNCE_TIME_MS_D      50U      /* Default key debounce time (milliseconds) */
#define KEYHANDLE_SINGLE_KEY_LONG_PRESS_TIME_MS_D    1000U    /* Default long press detection time (milliseconds) */
#define KEYHANDLE_SINGLE_KEY_LONG_PRESS_REPEAT_MS_D  200U     /* Default long press repeat trigger period (milliseconds) */

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateScan(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateDebounce(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateDetectSingle(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateDetectCombo(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_SINGLE_KEY_E KeyHandle_eMatrixToSingleKey(const KEYHANDLE_HANDLE_T* const psHandle, U8 u8MatrixKey);
static BOOL KeyHandle_bIsComboMatch(const KEYHANDLE_COMBO_KEY_MAP_T* const psCombo, const KEYHANDLE_SINGLE_KEY_E* const aeActiveKeys, U8 u8KeyCount);
static void KeyHandle_vTriggerEvent(const KEYHANDLE_HANDLE_T* const psHandle, const KEYHANDLE_EVENT_T* const psEvent);
static U32 KeyHandle_u32GetTickMs(void);
static void KeyHandle_vResetSingleKeyStates(KEYHANDLE_HANDLE_T* const psHandle);
static void KeyHandle_vResetState(KEYHANDLE_HANDLE_T* const psHandle);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize key handling module.
* @remark None.
*
* @param psHandle [in]: Key handling handle pointer.
* @return Initialization status.
*/
KEYHANDLE_STATUS_E KeyHandle_eInit(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if (psHandle == NULL_D)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    HALKEY_CONFIG_T sHalConfig;
    if (HalKey_eGetDefaultConfig(&sHalConfig) != HALKEY_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }

    /* Initialize HalKey */
    if (HalKey_eInit(&psHandle->sHalKey, &sHalConfig) != HALKEY_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    psHandle->sConfig.u32SingleKeyDebounceTimeMs = KEYHANDLE_SINGLE_KEY_DEBOUNCE_TIME_MS_D;
    psHandle->sConfig.u32SingleKeyLongPressTimeMs = KEYHANDLE_SINGLE_KEY_LONG_PRESS_TIME_MS_D;
    psHandle->sConfig.u32SingleKeyLongPressRepeatMs = KEYHANDLE_SINGLE_KEY_LONG_PRESS_REPEAT_MS_D;

    /* Initialize default key mapping configuration */
    psHandle->sConfig.asSingleKeyMaps[0U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E, 0U};    /* Left function key->Matrix position 0 */
    psHandle->sConfig.asSingleKeyMaps[1U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E, 1U};   /* Right function key->Matrix position 1 */
    psHandle->sConfig.asSingleKeyMaps[2U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_UP_E, 2U};           /* Up direction key->Matrix position 2 */
    psHandle->sConfig.asSingleKeyMaps[3U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_DOWN_E, 3U};         /* Down direction key->Matrix position 3 */
    psHandle->sConfig.asSingleKeyMaps[4U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LEFT_E, 4U};         /* Left direction key->Matrix position 4 */
    psHandle->sConfig.asSingleKeyMaps[5U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_RIGHT_E, 5U};        /* Right direction key->Matrix position 5 */
    psHandle->sConfig.asSingleKeyMaps[6U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_START_E, 6U};        /* Start key->Matrix position 6 */
    psHandle->sConfig.asSingleKeyMaps[7U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_STOP_E, 7U};         /* Stop key->Matrix position 7 */
    psHandle->sConfig.asSingleKeyMaps[8U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_HELP_E, 8U};         /* Help key->Matrix position 8 */
    psHandle->sConfig.asSingleKeyMaps[9U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LOCAL_REMOTE_E, 9U}; /* Local/Remote key->Matrix position 9 */
    
    /* Initialize remaining mapping positions to invalid state */
    for (U8 i = 10U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psHandle->sConfig.asSingleKeyMaps[i] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_MAX_E, 0xFFU};
    }
    
    /* Initialize default combination key mapping */
    psHandle->sConfig.asComboKeyMaps[0U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_RIGHT_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_RIGHT_E}};     /* Left+Right direction keys */
    psHandle->sConfig.asComboKeyMaps[1U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_UP_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_UP_E}};           /* Left+Up direction keys */
    psHandle->sConfig.asComboKeyMaps[2U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_DOWN_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_DOWN_E}};       /* Left+Down direction keys */
    psHandle->sConfig.asComboKeyMaps[3U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_RIGHT_UP_E, 2U, {KEYHANDLE_SINGLE_KEY_RIGHT_E, KEYHANDLE_SINGLE_KEY_UP_E}};         /* Right+Up direction keys */
    psHandle->sConfig.asComboKeyMaps[4U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_RIGHT_DOWN_E, 2U, {KEYHANDLE_SINGLE_KEY_RIGHT_E, KEYHANDLE_SINGLE_KEY_DOWN_E}};     /* Right+Down direction keys */
    psHandle->sConfig.asComboKeyMaps[5U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_HELP_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_HELP_E}};       /* Left+Help keys */
    
    /* Initialize remaining combination key positions to invalid state */
    for (U8 i = 6U; i < KEYHANDLE_COMBO_KEY_MAX_E; i++)
    {
        psHandle->sConfig.asComboKeyMaps[i] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_MAX_E, 0U, {0}};
    }
    
    /* Initialize callback function arrays to null */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psHandle->sConfig.apvSingleKeyShortPressCallbacks[i] = NULL_D;
        psHandle->sConfig.apvSingleKeyLongPressCallbacks[i] = NULL_D;
    }
    
    for (U8 i = 0U; i < KEYHANDLE_COMBO_KEY_MAX_E; i++)
    {
        psHandle->sConfig.apvComboKeyShortPressCallbacks[i] = NULL_D;
    }
    
    KeyHandle_vResetState(psHandle);
    
    psHandle->bInitialized = TRUE_D;
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Process key scanning and event detection using state machine.
* @remark Main processing function implemented as state machine.
*
* @param psHandle [in]: Key handling handle pointer.
* @return Processing status.
*/
KEYHANDLE_STATUS_E KeyHandle_eProcess(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    KEYHANDLE_STATE_MACHINE_E eNextStateMachine = KEYHANDLE_STATE_SCAN_E;
    
    switch (psHandle->sState.eCurrentStateMachine)
    {
        case KEYHANDLE_STATE_SCAN_E:
            eNextStateMachine = KeyHandle_eProcessStateScan(psHandle);
            break;
            
        case KEYHANDLE_STATE_DEBOUNCE_E:
            eNextStateMachine = KeyHandle_eProcessStateDebounce(psHandle);
            break;
            
        case KEYHANDLE_STATE_DETECT_SINGLE_E:
            eNextStateMachine = KeyHandle_eProcessStateDetectSingle(psHandle);
            break;
            
        case KEYHANDLE_STATE_DETECT_COMBO_E:
            eNextStateMachine = KeyHandle_eProcessStateDetectCombo(psHandle);
            break;
            
        default:
            break;
    }

    psHandle->sState.eCurrentStateMachine = eNextStateMachine;
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Set single key short press callback function.
* @remark Set callback function for specific single key short press event.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eSingleKey [in]: Single key type.
* @param pvCallback [in]: Event callback function pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eSetSingleKeyShortPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, void (*pvCallback)(const KEYHANDLE_EVENT_T* const psEvent))
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eSingleKey >= KEYHANDLE_SINGLE_KEY_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Set single key short press callback function */
    psHandle->sConfig.apvSingleKeyShortPressCallbacks[eSingleKey] = pvCallback;

    return KEYHANDLE_OK_E;
}

/**
* @brief Set single key long press callback function.
* @remark Set callback function for specific single key long press event.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eSingleKey [in]: Single key type.
* @param pvCallback [in]: Event callback function pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eSetSingleKeyLongPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, void (*pvCallback)(const KEYHANDLE_EVENT_T* const psEvent))
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eSingleKey >= KEYHANDLE_SINGLE_KEY_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Set single key long press callback function */
    psHandle->sConfig.apvSingleKeyLongPressCallbacks[eSingleKey] = pvCallback;
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Set combination key short press callback function.
* @remark Set callback function for specific combination key short press event.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eComboKey [in]: Combination key type.
* @param pvCallback [in]: Event callback function pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eSetComboKeyShortPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_COMBO_KEY_E eComboKey, void (*pvCallback)(const KEYHANDLE_EVENT_T* const psEvent))
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eComboKey >= KEYHANDLE_COMBO_KEY_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Set combination key short press callback function */
    psHandle->sConfig.apvComboKeyShortPressCallbacks[eComboKey] = pvCallback;

    return KEYHANDLE_OK_E;
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief State machine scan state handler.
* @remark Scan key matrix and get current raw key state.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateScan(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and retry scan */
        KeyHandle_vResetState(psHandle);

        return KEYHANDLE_STATE_SCAN_E;
    }
    
    /* Save previous debounced state for edge detection */
    psHandle->sState.u16PreviousKeyMatrix = psHandle->sState.u16DebouncedKeyMatrix;
    
    /* Scan key matrix, get current raw key state */
    if (HalKey_eScanMatrix(&psHandle->sHalKey, &psHandle->sState.u16CurrentKeyMatrix) != HALKEY_OK_E)
    {
        /* Hardware scan failed - reset state and retry from scan state */
        KeyHandle_vResetState(psHandle);

        return KEYHANDLE_STATE_SCAN_E;
    }
    
    /* Transition to debounce state */
    return KEYHANDLE_STATE_DEBOUNCE_E;
}

/**
* @brief State machine debounce state handler.
* @remark Perform key debounce processing and decide next state based on key count.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateDebounce(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and return to scan */
        KeyHandle_vResetState(psHandle);

        return KEYHANDLE_STATE_SCAN_E;
    }
    
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();

    /* Calculate state change bitmap */
    U16 u16Changed = psHandle->sState.u16CurrentKeyMatrix ^ psHandle->sState.u16PreviousKeyMatrix;
    
    /* Perform debounce processing for each key */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key state changed */
        if (u16Changed & (1U << i))
        {
            /* Key state changed, restart debounce timing */
            psHandle->sState.au32SingleKeyDebounceTime[i] = u32CurrentTime;
        }
        /* Check if debounce time has elapsed */
        else if ((u32CurrentTime - psHandle->sState.au32SingleKeyDebounceTime[i]) >= psHandle->sConfig.u32SingleKeyDebounceTimeMs)
        {
            /* Debounce time has elapsed, update debounced key state */
            if (psHandle->sState.u16CurrentKeyMatrix & (1U << i))
            {
                /* Set key pressed state */
                psHandle->sState.u16DebouncedKeyMatrix |= (1U << i);
            }
            else
            {
                /* Clear key pressed state */
                psHandle->sState.u16DebouncedKeyMatrix &= ~(1U << i);
            }
        }
    }

    psHandle->sState.u8CurrentKeyCount = 0U;
    
    /* Count pressed keys in debounced matrix */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        if (psHandle->sState.u16DebouncedKeyMatrix & (1U << i))
        {
            /* Check if this matrix key is mapped to a valid single key */
            KEYHANDLE_SINGLE_KEY_E eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);

            if (eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                psHandle->sState.u8CurrentKeyCount++;
            }
        }
    }
    
    /* Decide next state based on current key count */
    if (psHandle->sState.u8CurrentKeyCount == 0U)
    {
        /* No keys pressed - clear combo mode and return to scan */
        psHandle->sState.bInComboMode = FALSE_D;

        return KEYHANDLE_STATE_SCAN_E;
    }
    else if (psHandle->sState.u8CurrentKeyCount == 1U)
    {
        /* Single key pressed */
        if (psHandle->sState.bInComboMode)
        {
            /* Coming from combo mode - ignore this single key and return to scan */
            return KEYHANDLE_STATE_SCAN_E;
        }
        else
        {
            /* Normal single key processing */
            return KEYHANDLE_STATE_DETECT_SINGLE_E;
        }
    }
    else
    {
        /* Multiple keys pressed - enter combo mode */
        if (!psHandle->sState.bInComboMode)
        {
            /* Entering combo mode - reset single key states */
            KeyHandle_vResetSingleKeyStates(psHandle);

            psHandle->sState.bInComboMode = TRUE_D;
        }

        return KEYHANDLE_STATE_DETECT_COMBO_E;
    }
}

/**
* @brief State machine single key detection state handler.
* @remark Detect single key events (press, release, short press, long press).
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateDetectSingle(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and return to scan */
        KeyHandle_vResetState(psHandle);

        return KEYHANDLE_STATE_SCAN_E;
    }
    
    /* Detect single key events */
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();
    
    /* Currently pressed keys */
    U16 u16PressedKeys = psHandle->sState.u16DebouncedKeyMatrix;

    /* Just released keys */
    U16 u16ReleasedKeys = psHandle->sState.u16PreviousKeyMatrix & (~psHandle->sState.u16DebouncedKeyMatrix);

    /* Just pressed keys */
    U16 u16NewPressedKeys = psHandle->sState.u16DebouncedKeyMatrix & (~psHandle->sState.u16PreviousKeyMatrix);
    
    /* Process new key press state */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        if (u16NewPressedKeys & (1U << i))
        {
            /* Record press time */
            psHandle->sState.au32SingleKeyPressTime[i] = u32CurrentTime;
            
            /* Clear processed flag */
            psHandle->sState.abSingleKeyLongPressProcessed[i] = FALSE_D;
        }
    }
    
    /* Process key release state */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key is released */
        if (u16ReleasedKeys & (1U << i))
        {
            /* Clear processed flag and long press trigger time */
            psHandle->sState.abSingleKeyLongPressProcessed[i] = FALSE_D;
            psHandle->sState.au32SingleKeyLastLongPressTime[i] = 0U;
        }
    }
    
    /* Detect and trigger short press events */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key is released */
        if (u16ReleasedKeys & (1U << i))
        {
            /* Calculate press duration */
            U32 u32PressDuration = u32CurrentTime - psHandle->sState.au32SingleKeyPressTime[i];
            /* Check if short press conditions are met */
            if (u32PressDuration < psHandle->sConfig.u32SingleKeyLongPressTimeMs)
            {
                /* Construct and trigger short press event */
                KEYHANDLE_EVENT_T sEvent = {0U};
                sEvent.eEventType = KEYHANDLE_EVENT_SINGLE_SHORT_PRESS_E;
                sEvent.sKeyEventData.eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);
                sEvent.sKeyEventData.u8MatrixKey = i;
                sEvent.u32Timestamp = u32CurrentTime;
                KeyHandle_vTriggerEvent(psHandle, &sEvent);
            }
        }
    }
    
    /* Process long press event detection */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key is pressed */
        if ((u16PressedKeys & (1U << i)) != 0U)
        {
            /* Calculate press duration */
            U32 u32PressDuration = u32CurrentTime - psHandle->sState.au32SingleKeyPressTime[i];
            
            /* Check if initial long press conditions are met */
            if ((u32PressDuration >= psHandle->sConfig.u32SingleKeyLongPressTimeMs) && (!psHandle->sState.abSingleKeyLongPressProcessed[i]))
            {
                /* First long press trigger */
                KEYHANDLE_EVENT_T sEvent = {0U};
                sEvent.eEventType = KEYHANDLE_EVENT_SINGLE_LONG_PRESS_E;
                sEvent.sKeyEventData.eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);
                sEvent.sKeyEventData.u8MatrixKey = i;
                sEvent.u32Timestamp = u32CurrentTime;
                KeyHandle_vTriggerEvent(psHandle, &sEvent);
                
                /* Mark as processed and record first trigger time */
                psHandle->sState.abSingleKeyLongPressProcessed[i] = TRUE_D;
                psHandle->sState.au32SingleKeyLastLongPressTime[i] = u32CurrentTime;
            }
            /* Check for repeat long press triggers */
            else if (psHandle->sState.abSingleKeyLongPressProcessed[i] && 
                    ((u32CurrentTime - psHandle->sState.au32SingleKeyLastLongPressTime[i]) >= psHandle->sConfig.u32SingleKeyLongPressRepeatMs))
            {
                /* Repeat long press trigger */
                KEYHANDLE_EVENT_T sEvent = {0U};
                sEvent.eEventType = KEYHANDLE_EVENT_SINGLE_LONG_PRESS_E;
                sEvent.sKeyEventData.eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);
                sEvent.sKeyEventData.u8MatrixKey = i;
                sEvent.u32Timestamp = u32CurrentTime;
                KeyHandle_vTriggerEvent(psHandle, &sEvent);
                
                /* Update last trigger time for next repeat */
                psHandle->sState.au32SingleKeyLastLongPressTime[i] = u32CurrentTime;
            }
        }
    }
    
    /* Return to scan state to complete single key processing cycle */
    return KEYHANDLE_STATE_SCAN_E;
}

/**
* @brief State machine combination key detection state handler.
* @remark Detect combination key events.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eProcessStateDetectCombo(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and return to scan */
        KeyHandle_vResetState(psHandle);
        return KEYHANDLE_STATE_SCAN_E;
    }
    
    /* Detect combination key events */
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();
    
    /* Get list of currently pressed single keys */
    KEYHANDLE_SINGLE_KEY_E aeCurrentKeys[KEYHANDLE_SINGLE_KEY_MAX_E];

    U8 u8CurrentKeyCount = 0U;
    
    /* Get list of currently pressed single keys */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key is pressed */
        if (psHandle->sState.u16DebouncedKeyMatrix & (1U << i))
        {
            /* Get corresponding single key type */
            KEYHANDLE_SINGLE_KEY_E eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);

            if (eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                /* Add to current key list */
                aeCurrentKeys[u8CurrentKeyCount++] = eSingleKey;
            }
        }
    }
    
    /* Traverse all combination key mappings, check if there are matching combinations */
    for (U8 i = 0U; i < KEYHANDLE_COMBO_KEY_MAX_E; i++)
    {
        /* Check if combination key is enabled */
        if (psHandle->sConfig.asComboKeyMaps[i].u8KeyCount > 0U)
        {
            /* Check if combination key matches the current pressed keys */
            if (KeyHandle_bIsComboMatch(&psHandle->sConfig.asComboKeyMaps[i], aeCurrentKeys, u8CurrentKeyCount))
            {
                /* Check if it's a new combination key (avoid repeated triggering) */ 
                BOOL bNewCombo = (psHandle->sState.u8ComboKeyActiveKeyCount != u8CurrentKeyCount);

                if (!bNewCombo)
                {
                    /* Further check if combination content has changed */
                    for (U8 j = 0U; j < u8CurrentKeyCount; j++)
                    {
                        BOOL bFound = FALSE_D;

                        /* Check if key is in the current active combination key list */
                        for (U8 k = 0U; k < psHandle->sState.u8ComboKeyActiveKeyCount; k++)
                        {
                            if (aeCurrentKeys[j] == psHandle->sState.aeComboKeyActiveKeys[k])
                            {
                                bFound = TRUE_D;
                                break;
                            }
                        }

                        /* Check if key is not in the current active combination key list */
                        if (!bFound)
                        {
                            bNewCombo = TRUE_D;
                            break;
                        }
                    }
                }

                /* If combination key is new */
                if (bNewCombo)
                {
                    /* Construct and trigger combination key short press event */
                    KEYHANDLE_EVENT_T sEvent = {0U};
                    sEvent.eEventType = KEYHANDLE_EVENT_COMBO_SHORT_PRESS_E;
                    sEvent.sComboEventData.eComboKey = psHandle->sConfig.asComboKeyMaps[i].eComboKey;
                    sEvent.sComboEventData.u8KeyCount = psHandle->sConfig.asComboKeyMaps[i].u8KeyCount;
                    memmove(sEvent.sComboEventData.aeKeys, 
                            psHandle->sConfig.asComboKeyMaps[i].aeKeys,
                            sizeof(KEYHANDLE_SINGLE_KEY_E) * psHandle->sConfig.asComboKeyMaps[i].u8KeyCount);
                    sEvent.u32Timestamp = u32CurrentTime;
                    KeyHandle_vTriggerEvent(psHandle, &sEvent);
                    
                    /* Update active combination key state */
                    psHandle->sState.u8ComboKeyActiveKeyCount = u8CurrentKeyCount;
                    memmove(psHandle->sState.aeComboKeyActiveKeys, aeCurrentKeys, sizeof(KEYHANDLE_SINGLE_KEY_E) * u8CurrentKeyCount);
                }

                /* Exit loop after finding matching combination key */
                break;
            }
        }
    }
    
    /* Clear combination key state when no keys are pressed */
    if (u8CurrentKeyCount == 0U)
    {
        psHandle->sState.u8ComboKeyActiveKeyCount = 0U;
    }
    
    /* Return to scan state to complete the cycle */
    return KEYHANDLE_STATE_SCAN_E;
}

/**
* @brief Reset state machine state to recover from error conditions.
* @remark Clear all state information and prepare for restart from scan state.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Nothing.
*/
void KeyHandle_vResetState(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity and initialization check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return;
    }

    /* Reset state machine and clear all state variables */
    psHandle->sState.eCurrentStateMachine = KEYHANDLE_STATE_SCAN_E;
    psHandle->sState.u16CurrentKeyMatrix = 0U;
    psHandle->sState.u16PreviousKeyMatrix = 0U;
    psHandle->sState.u16DebouncedKeyMatrix = 0U;
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psHandle->sState.au32SingleKeyPressTime[i] = 0U;
        psHandle->sState.au32SingleKeyDebounceTime[i] = 0U;
        psHandle->sState.au32SingleKeyLastLongPressTime[i] = 0U;
        psHandle->sState.abSingleKeyLongPressProcessed[i] = FALSE_D;
    }
    psHandle->sState.u8ComboKeyActiveKeyCount = 0U;
    for (U8 i = 0U; i < KEYHANDLE_MAX_COMBO_SIZE_D; i++)
    {
        psHandle->sState.aeComboKeyActiveKeys[i] = KEYHANDLE_SINGLE_KEY_MAX_E;
    }
    psHandle->sState.bInComboMode = FALSE_D;
    psHandle->sState.u8CurrentKeyCount = 0U;
}


















/**
* @brief Convert matrix key index to single key type.
* @remark None.
*
* @param psHandle [in]: Key handling handle pointer.
* @param u8MatrixKey [in]: Matrix key index (0-15).
* @return Corresponding single key type.
*/
KEYHANDLE_SINGLE_KEY_E KeyHandle_eMatrixToSingleKey(const KEYHANDLE_HANDLE_T* const psHandle, U8 u8MatrixKey)
{
    /* Parameter validity and initialization check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return KEYHANDLE_SINGLE_KEY_MAX_E;
    }
    
    /* Search for corresponding single key type in mapping table */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        if ((psHandle->sConfig.asSingleKeyMaps[i].eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E) && (psHandle->sConfig.asSingleKeyMaps[i].u8MatrixKey == u8MatrixKey))
        {
            return psHandle->sConfig.asSingleKeyMaps[i].eSingleKey;
        }
    }
    
    return KEYHANDLE_SINGLE_KEY_MAX_E;
}

/**
* @brief Check if combination key matches.
* @remark Check if combination key matches, return TRUE_D if all keys in the combination are in the current active key list.
*
* @param psCombo [in]: Combination key mapping pointer.
* @param aeActiveKeys [in]: Current active key list.
* @param u8KeyCount [in]: Number of currently active keys.
* @return Whether matches (TRUE_D-match, FALSE_D-no match).
*/
BOOL KeyHandle_bIsComboMatch(const KEYHANDLE_COMBO_KEY_MAP_T* const psCombo, const KEYHANDLE_SINGLE_KEY_E* const aeActiveKeys, U8 u8KeyCount)
{
    /* Parameter validity and key count check */
    if ((psCombo == NULL_D) || (aeActiveKeys == NULL_D) || (u8KeyCount != psCombo->u8KeyCount))
    {
        return FALSE_D;
    }
    
    /* Check if all keys in the combination are in the current active key list */
    for (U8 i = 0U; i < psCombo->u8KeyCount; i++)
    {
        BOOL bFound = FALSE_D;

        for (U8 j = 0U; j < u8KeyCount; j++)
        {
            /* Check if key is in the current active key list */
            if (psCombo->aeKeys[i] == aeActiveKeys[j])
            {
                bFound = TRUE_D;
                break;
            }
        }

        if (!bFound)
        {
            return FALSE_D;
        }
    }
    
    return TRUE_D;
}

/**
* @brief Trigger key event.
* @remark Call appropriate registered callback function based on event type and key.
*
* @param psHandle [in]: Key handling handle pointer.
* @param psEvent [in]: Event structure pointer.
* @return Nothing.
*/
void KeyHandle_vTriggerEvent(const KEYHANDLE_HANDLE_T* const psHandle, const KEYHANDLE_EVENT_T* const psEvent)
{
    /* Check parameter validity */
    if ((psHandle == NULL_D) || (psEvent == NULL_D))
    {
        return;
    }
    
    void (*pvCallback)(const KEYHANDLE_EVENT_T* const psEvent) = NULL_D;
    
    /* Get appropriate callback function based on event type */
    switch (psEvent->eEventType)
    {
        case KEYHANDLE_EVENT_SINGLE_SHORT_PRESS_E:
            /* Single key short press event */
            if (psEvent->sKeyEventData.eSingleKey < KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                pvCallback = psHandle->sConfig.apvSingleKeyShortPressCallbacks[psEvent->sKeyEventData.eSingleKey];
            }
            break;
            
        case KEYHANDLE_EVENT_SINGLE_LONG_PRESS_E:
            /* Single key long press event */
            if (psEvent->sKeyEventData.eSingleKey < KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                pvCallback = psHandle->sConfig.apvSingleKeyLongPressCallbacks[psEvent->sKeyEventData.eSingleKey];
            }
            break;
            
        case KEYHANDLE_EVENT_COMBO_SHORT_PRESS_E:
            /* Combination key short press event */
            if (psEvent->sComboEventData.eComboKey < KEYHANDLE_COMBO_KEY_MAX_E)
            {
                pvCallback = psHandle->sConfig.apvComboKeyShortPressCallbacks[psEvent->sComboEventData.eComboKey];
            }
            break;
            
        default:
            /* Unknown event type */
                        break;
                    }
    
    /* Call callback function if it's registered */
    if (pvCallback != NULL_D)
    {
        pvCallback(psEvent);
    }
}

/**
* @brief Get system clock milliseconds.
* @remark Use FreeRTOS system clock function to get millisecond-level timestamp.
*
* @return Current system clock milliseconds.
*/
U32 KeyHandle_u32GetTickMs(void)
{
    U32 xTicks = (U32)xTaskGetTickCount();

    return xTicks * portTICK_PERIOD_MS;
}



/**
* @brief Reset single key states to prevent interference with combo key processing.
* @remark Clear all single key related timing and processing states.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Nothing.
*/
void KeyHandle_vResetSingleKeyStates(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity and initialization check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return;
    }
    
    /* Clear all single key timing states */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psHandle->sState.au32SingleKeyPressTime[i] = 0U;
        psHandle->sState.au32SingleKeyLastLongPressTime[i] = 0U;
        psHandle->sState.abSingleKeyLongPressProcessed[i] = FALSE_D;
    }
}

//===========================================================================
// End of file.
//===========================================================================








