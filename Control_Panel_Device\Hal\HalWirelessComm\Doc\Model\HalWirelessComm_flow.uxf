<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>7</zoom_level>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>224</x>
      <y>42</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>182</x>
      <y>77</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>49</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>217</x>
      <y>133</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>105</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>182</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check config param
Validate psConfig</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>154</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointers]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>217</x>
      <y>238</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>210</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>287</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Save config params
Copy to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>259</y>
      <w>112</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid UART handle]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>343</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Initialize handle
Clear flags and buffers</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>315</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>399</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Enable UART interrupts
IDLE and ERROR</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>371</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>455</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set initialized flag
Mark as ready</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>427</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>161</x>
      <y>511</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>483</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>224</x>
      <y>588</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>532</y>
      <w>21</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>308</x>
      <y>511</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>133</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Null pointers]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>238</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Null UART handle]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>140</y>
      <w>21</w>
      <h>385</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;530.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>532</y>
      <w>175</w>
      <h>42</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;230.0;40.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>126</x>
      <y>0</y>
      <w>364</w>
      <h>623</h>
    </coordinates>
    <panel_attributes>HalWirelessComm Init
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>616</x>
      <y>42</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>574</x>
      <y>77</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>49</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>609</x>
      <y>133</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>105</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>609</x>
      <y>238</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>567</x>
      <y>287</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Stop operations
Both Rx and Tx</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>154</y>
      <w>77</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>259</y>
      <w>63</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>567</x>
      <y>343</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Disable interrupts
IDLE and ERROR</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>315</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>567</x>
      <y>399</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Clear handle data
Reset all flags</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>371</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>553</x>
      <y>455</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>427</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>616</x>
      <y>532</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>476</y>
      <w>21</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>700</x>
      <y>455</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>133</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>238</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>777</x>
      <y>140</y>
      <w>21</w>
      <h>329</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;450.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>476</y>
      <w>182</w>
      <h>42</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;240.0;40.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>567</x>
      <y>182</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check initialized flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>616</x>
      <y>210</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>525</x>
      <y>0</y>
      <w>364</w>
      <h>567</h>
    </coordinates>
    <panel_attributes>HalWirelessComm DeInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>357</x>
      <y>707</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>315</x>
      <y>742</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>714</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>350</x>
      <y>798</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>770</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>350</x>
      <y>903</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>819</y>
      <w>112</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid and initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>924</y>
      <w>70</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not active]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>308</x>
      <y>952</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Clear Rx flags
Length and complete</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>308</x>
      <y>1008</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Start DMA receive
To idle mode</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>980</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>350</x>
      <y>1064</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>1036</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>308</x>
      <y>1120</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set Rx active flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>1085</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[DMA success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>294</x>
      <y>1176</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>1148</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>357</x>
      <y>1260</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>1197</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>441</x>
      <y>1176</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>133</x>
      <y>1176</y>
      <w>154</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_BUSY_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>371</x>
      <y>798</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>203</x>
      <y>903</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Already active]</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>371</x>
      <y>1064</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[DMA failed]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>518</x>
      <y>805</y>
      <w>21</w>
      <h>385</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;530.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>203</x>
      <y>910</y>
      <w>21</w>
      <h>280</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;380.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>1197</y>
      <w>182</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;240.0;50.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>308</x>
      <y>847</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check Rx active flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>357</x>
      <y>875</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>126</x>
      <y>665</y>
      <w>504</w>
      <h>679</h>
    </coordinates>
    <panel_attributes>HalWirelessComm Receive
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>896</x>
      <y>707</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>854</x>
      <y>742</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>714</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>889</x>
      <y>798</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>770</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>889</x>
      <y>903</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>819</y>
      <w>119</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params and init]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>924</y>
      <w>70</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not active]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>847</x>
      <y>952</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Clear Tx flags
Complete flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>847</x>
      <y>1008</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Start DMA transmit
Send data buffer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>980</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>889</x>
      <y>1064</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>1036</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>847</x>
      <y>1120</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set Tx active flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>1085</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[DMA success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>833</x>
      <y>1176</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>1148</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>896</x>
      <y>1267</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>1197</y>
      <w>21</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>987</x>
      <y>1176</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>665</x>
      <y>1176</y>
      <w>154</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_BUSY_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>910</x>
      <y>798</y>
      <w>175</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>230.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>742</x>
      <y>903</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Already active]</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>910</x>
      <y>1064</y>
      <w>175</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[DMA failed]</panel_attributes>
    <additional_attributes>230.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1064</x>
      <y>805</y>
      <w>21</w>
      <h>385</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;530.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>742</x>
      <y>910</y>
      <w>21</w>
      <h>280</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;380.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>1197</y>
      <w>189</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;250.0;50.0;250.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>847</x>
      <y>847</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check Tx active
Is already sending</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>896</x>
      <y>875</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>658</x>
      <y>665</y>
      <w>497</w>
      <h>679</h>
    </coordinates>
    <panel_attributes>HalWirelessComm Transmit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1022</x>
      <y>1435</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>980</x>
      <y>1470</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>1442</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1015</x>
      <y>1526</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>1498</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>973</x>
      <y>1575</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Get Rx length
From handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>1547</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>959</x>
      <y>1631</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>1603</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1022</x>
      <y>1715</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>1652</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1106</x>
      <y>1631</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1036</x>
      <y>1526</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1183</x>
      <y>1533</y>
      <w>21</w>
      <h>112</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;140.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>1652</y>
      <w>182</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;240.0;50.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>903</x>
      <y>1393</y>
      <w>392</w>
      <h>343</h>
    </coordinates>
    <panel_attributes>HalWirelessComm GetRxLength
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>224</x>
      <y>1855</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>182</x>
      <y>1890</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>1862</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>217</x>
      <y>1946</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>1918</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>1967</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>1995</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set Tx length
Update handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>161</x>
      <y>2051</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>2023</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>224</x>
      <y>2135</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>2072</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>308</x>
      <y>2051</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>1946</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1953</y>
      <w>21</w>
      <h>112</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;140.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>2072</y>
      <w>175</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>105</x>
      <y>1813</y>
      <w>392</w>
      <h>343</h>
    </coordinates>
    <panel_attributes>HalWirelessComm SetTxLength
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>658</x>
      <y>1855</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>616</x>
      <y>1890</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>658</x>
      <y>1862</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>651</x>
      <y>1946</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>658</x>
      <y>1918</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>609</x>
      <y>1995</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Get Tx buffer pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>658</x>
      <y>1967</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>595</x>
      <y>2051</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>658</x>
      <y>2023</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>658</x>
      <y>2135</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>658</x>
      <y>2072</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>742</x>
      <y>2051</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>672</x>
      <y>1946</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>812</x>
      <y>1953</y>
      <w>21</w>
      <h>112</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;140.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>658</x>
      <y>2072</y>
      <w>175</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>539</x>
      <y>1813</y>
      <w>392</w>
      <h>343</h>
    </coordinates>
    <panel_attributes>HalWirelessComm GetTxBuffer
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1092</x>
      <y>1855</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1050</x>
      <y>1890</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>1862</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1085</x>
      <y>1946</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>1918</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1043</x>
      <y>1995</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Get Rx buffer pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>1967</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1029</x>
      <y>2051</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>2023</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1092</x>
      <y>2135</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>2072</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1176</x>
      <y>2051</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1106</x>
      <y>1946</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1246</x>
      <y>1953</y>
      <w>21</w>
      <h>112</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;140.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>2072</y>
      <w>175</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>973</x>
      <y>1813</y>
      <w>392</w>
      <h>343</h>
    </coordinates>
    <panel_attributes>HalWirelessComm GetRxBuffer
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1022</x>
      <y>42</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>980</x>
      <y>77</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>49</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1015</x>
      <y>133</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>105</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1036</x>
      <y>133</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>966</x>
      <y>189</y>
      <w>126</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set default UART handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>154</y>
      <w>77</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>973</x>
      <y>245</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set bContinuousRx flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>217</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>273</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>959</x>
      <y>301</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1022</x>
      <y>378</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>322</y>
      <w>21</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1022</x>
      <y>322</y>
      <w>182</w>
      <h>42</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;240.0;40.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>931</x>
      <y>0</y>
      <w>357</w>
      <h>413</h>
    </coordinates>
    <panel_attributes>HalWirelessComm GetDefaultConfig
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1183</x>
      <y>140</y>
      <w>21</w>
      <h>175</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;230.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1106</x>
      <y>301</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>203</x>
      <y>1197</y>
      <w>175</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>230.0;50.0;10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>742</x>
      <y>1197</y>
      <w>175</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>230.0;50.0;10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>210</x>
      <y>2254</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>168</x>
      <y>2289</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>210</x>
      <y>2261</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>203</x>
      <y>2345</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>210</x>
      <y>2317</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>224</x>
      <y>2345</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>161</x>
      <y>2401</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set RX complete flag
Clear RX length</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>210</x>
      <y>2366</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>147</x>
      <y>2457</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>210</x>
      <y>2429</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>210</x>
      <y>2541</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>210</x>
      <y>2478</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>210</x>
      <y>2478</y>
      <w>175</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>105</x>
      <y>2212</y>
      <w>385</w>
      <h>364</h>
    </coordinates>
    <panel_attributes>HalWirelessComm ClearRxComplete
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>294</x>
      <y>2457</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>364</x>
      <y>2352</y>
      <w>21</w>
      <h>119</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>644</x>
      <y>2254</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>602</x>
      <y>2289</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>644</x>
      <y>2261</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>637</x>
      <y>2345</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>644</x>
      <y>2317</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>658</x>
      <y>2345</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>595</x>
      <y>2401</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set TX complete flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>644</x>
      <y>2366</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>581</x>
      <y>2457</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>644</x>
      <y>2429</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>644</x>
      <y>2541</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>644</x>
      <y>2478</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>644</x>
      <y>2478</y>
      <w>175</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>539</x>
      <y>2212</y>
      <w>392</w>
      <h>364</h>
    </coordinates>
    <panel_attributes>HalWirelessComm ClearTxComplete
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>728</x>
      <y>2457</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>798</x>
      <y>2352</y>
      <w>21</w>
      <h>119</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>581</x>
      <y>1435</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>539</x>
      <y>1470</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>581</x>
      <y>1442</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>574</x>
      <y>1526</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>581</x>
      <y>1498</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>532</x>
      <y>1575</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Get ContinuousRx flag
From handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>581</x>
      <y>1547</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>518</x>
      <y>1631</y>
      <w>140</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>581</x>
      <y>1603</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>581</x>
      <y>1715</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>581</x>
      <y>1652</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>665</x>
      <y>1631</y>
      <w>161</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Return 
HALWIRELESSCOMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>595</x>
      <y>1526</y>
      <w>168</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>742</x>
      <y>1533</y>
      <w>21</w>
      <h>112</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;140.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>581</x>
      <y>1652</y>
      <w>182</w>
      <h>49</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;240.0;50.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>462</x>
      <y>1393</y>
      <w>392</w>
      <h>343</h>
    </coordinates>
    <panel_attributes>HalWirelessComm GetContinuousRx
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1281</x>
      <y>707</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1239</x>
      <y>742</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>714</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1274</x>
      <y>798</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>770</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1295</x>
      <y>798</y>
      <w>119</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1274</x>
      <y>973</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>819</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1295</x>
      <y>973</y>
      <w>119</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Not in busy state]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1232</x>
      <y>1029</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Get RX data length</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>994</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[In busy state]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1274</x>
      <y>1141</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>1057</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1295</x>
      <y>1141</y>
      <w>119</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Length = 0]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1211</x>
      <y>1197</y>
      <w>154</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>Set RX complete flags and length
Set state is COMPLETE_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>1162</y>
      <w>70</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Length &gt; 0]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1281</x>
      <y>1295</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>1232</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>805</y>
      <w>133</w>
      <h>476</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;660.0;170.0;660.0;170.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1183</x>
      <y>665</y>
      <w>266</w>
      <h>665</h>
    </coordinates>
    <panel_attributes>HalWirelessComm RxCompleteCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1085</x>
      <y>2254</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1043</x>
      <y>2289</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2261</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1078</x>
      <y>2345</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2317</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1099</x>
      <y>2345</y>
      <w>133</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>170.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1078</x>
      <y>2520</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2366</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1099</x>
      <y>2520</y>
      <w>133</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Not in busy state]</panel_attributes>
    <additional_attributes>170.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1029</x>
      <y>2576</y>
      <w>126</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set TX complete flags
Set state is COMPLETE_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2541</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[In busy state]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1085</x>
      <y>2674</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2604</y>
      <w>21</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2352</y>
      <w>147</w>
      <h>301</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;410.0;190.0;410.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>980</x>
      <y>2212</y>
      <w>308</w>
      <h>504</h>
    </coordinates>
    <panel_attributes>HalWirelessComm TxCompleteCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1232</x>
      <y>917</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check RX state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>945</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1232</x>
      <y>1085</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check RX length is 0</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>1113</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1036</x>
      <y>2464</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check TX state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2492</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1225</x>
      <y>854</y>
      <w>126</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Convert pointer to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1281</x>
      <y>882</y>
      <w>21</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1029</x>
      <y>2401</y>
      <w>126</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Convert pointer to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1085</x>
      <y>2429</y>
      <w>21</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>238</x>
      <y>1435</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>196</x>
      <y>1470</y>
      <w>98</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>1442</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>231</x>
      <y>1526</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>1498</y>
      <w>21</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>252</x>
      <y>1526</y>
      <w>112</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>140.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>189</x>
      <y>1645</y>
      <w>112</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Set error state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>1547</y>
      <w>84</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>238</x>
      <y>1736</y>
      <w>14</w>
      <h>14</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>1673</y>
      <w>21</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>1533</y>
      <w>126</w>
      <h>189</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;250.0;160.0;250.0;160.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>119</x>
      <y>1393</y>
      <w>287</w>
      <h>378</h>
    </coordinates>
    <panel_attributes>HalWirelessComm ErrorOccurCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>182</x>
      <y>1582</y>
      <w>126</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>Convert pointer to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>238</x>
      <y>1610</y>
      <w>21</w>
      <h>49</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
</diagram>
