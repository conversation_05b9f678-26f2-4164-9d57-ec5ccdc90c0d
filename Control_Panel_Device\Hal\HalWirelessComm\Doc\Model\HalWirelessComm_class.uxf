<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>7</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>434</x>
      <y>133</y>
      <w>875</w>
      <h>259</h>
    </coordinates>
    <panel_attributes>HalWirelessComm
--
+HalWirelessComm_eInit(psHandle:HALWIRELESSCOMM_HANDLE_T* const, psConfig:const HALWIRELESSCOMM_CONFIG_T* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eDeInit(psHandle:H<PERSON>WI<PERSON>LESSCOMM_HANDLE_T* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eGetDefaultConfig(psConfig:HALWIRELESSCOMM_CONFIG_T* const):HALWIRELESSCOMM_STATUS_E
+Hal<PERSON><PERSON>lessComm_eReceive(psHandle:HALWIRELESSCOMM_HANDLE_T* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eTransmit(psHandle:HALWIRELESSCOMM_HANDLE_T* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eGetTxState(psHandle:const HALWIRELESSCOMM_HANDLE_T* const, peTxState:HALWIRELESSCOMM_COMM_STATE_E* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eGetRxState(psHandle:const HALWIRELESSCOMM_HANDLE_T* const, peRxState:HALWIRELESSCOMM_COMM_STATE_E* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eGetRxLength(psHandle:const HALWIRELESSCOMM_HANDLE_T* const, pu16Length:U16* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eSetTxLength(psHandle:HALWIRELESSCOMM_HANDLE_T* const, u16Length:const U16):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eGetTxBuffer(psHandle:const HALWIRELESSCOMM_HANDLE_T* const, ppu8TxBuffer:U8** const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eGetRxBuffer(psHandle:const HALWIRELESSCOMM_HANDLE_T* const, ppu8RxBuffer:U8** const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eGetContinuousRx(psHandle:const HALWIRELESSCOMM_HANDLE_T* const, pbContinuousRx:BOOL* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eClearRxComplete(psHandle:HALWIRELESSCOMM_HANDLE_T* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_eClearTxComplete(psHandle:HALWIRELESSCOMM_HANDLE_T* const):HALWIRELESSCOMM_STATUS_E
+HalWirelessComm_vRxCompleteCallback(pvUserData:void* const):void
+HalWirelessComm_vTxCompleteCallback(pvUserData:void* const):void
+HalWirelessComm_vErrorOccurCallback(pvUserData:void* const):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>147</x>
      <y>140</y>
      <w>245</w>
      <h>56</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
HALWIRELESSCOMM_CONFIG_T
--
+psUartHandle:UART_HandleTypeDef*
+bContinuousRx:BOOL</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>84</x>
      <y>210</y>
      <w>308</w>
      <h>175</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
HALWIRELESSCOMM_HANDLE_T
--
+sConfig:HALWIRELESSCOMM_CONFIG_T
+eRxState:volatile HALWIRELESSCOMM_COMM_STATE_E
+eTxState:volatile HALWIRELESSCOMM_COMM_STATE_E
+u16RxLength:volatile U16
+u16TxLength:volatile U16
+au8RxBuffer[HALWIRELESSCOMM_RX_BUFFER_SIZE_D]:U8
+au8TxBuffer[HALWIRELESSCOMM_TX_BUFFER_SIZE_D]:U8
+bInitialized:BOOL</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>546</x>
      <y>0</y>
      <w>602</w>
      <h>98</h>
    </coordinates>
    <panel_attributes>HAL_UART
--
+HAL_UARTEx_ReceiveToIdle_DMA(huart:UART_HandleTypeDef*, pData:U8*, Size:U16):HAL_StatusTypeDef
+HAL_UART_Transmit_DMA(huart:UART_HandleTypeDef*, pData:U8*, Size:U16):HAL_StatusTypeDef
+HAL_UART_AbortReceive(huart:UART_HandleTypeDef*):HAL_StatusTypeDef
+HAL_UART_AbortTransmit(huart:UART_HandleTypeDef*):HAL_StatusTypeDef
+__HAL_UART_ENABLE_IT(huart:UART_HandleTypeDef*, interrupt:U32):void
+__HAL_UART_DISABLE_IT(huart:UART_HandleTypeDef*, interrupt:U32):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>91</y>
      <w>49</w>
      <h>56</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1351</x>
      <y>203</y>
      <w>189</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALWIRELESSCOMM_STATUS_E
--
HALWIRELESSCOMM_OK_E = 0U
HALWIRELESSCOMM_ERROR_E
HALWIRELESSCOMM_BUSY_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1351</x>
      <y>287</y>
      <w>231</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALWIRELESSCOMM_COMM_STATE_E
--
HALWIRELESSCOMM_COMM_IDLE_E = 0U
HALWIRELESSCOMM_COMM_BUSY_E
HALWIRELESSCOMM_COMM_COMPLETE_E
HALWIRELESSCOMM_COMM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1302</x>
      <y>217</y>
      <w>63</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1302</x>
      <y>315</y>
      <w>63</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>385</x>
      <y>154</y>
      <w>63</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>385</x>
      <y>294</y>
      <w>63</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
</diagram>
