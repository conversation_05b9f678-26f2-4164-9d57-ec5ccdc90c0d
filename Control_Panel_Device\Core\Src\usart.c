/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    usart.c
  * @brief   This file provides code for the configuration
  *          of the USART instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "usart.h"

/* USER CODE BEGIN 0 */
static UART_CALLBACK_FUNCTION_NODE_T* psUartIdleCallbackList = NULL;
static UART_CALLBACK_FUNCTION_NODE_T* psUartTxCompleteCallbackList = NULL;
static UART_CALLBACK_FUNCTION_NODE_T* psUartErrorCallbackList = NULL;

static UART_CALLBACK_FUNCTION_NODE_T* UartCallbackManager_psCreateNode(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData);
/* USER CODE END 0 */

UART_HandleTypeDef huart1;
UART_HandleTypeDef huart2;
UART_HandleTypeDef huart3;
DMA_HandleTypeDef hdma_usart1_rx;
DMA_HandleTypeDef hdma_usart1_tx;
DMA_HandleTypeDef hdma_usart2_rx;
DMA_HandleTypeDef hdma_usart2_tx;
DMA_HandleTypeDef hdma_usart3_rx;
DMA_HandleTypeDef hdma_usart3_tx;

/* USART1 init function */

void MX_USART1_UART_Init(void)
{

  /* USER CODE BEGIN USART1_Init 0 */

  /* USER CODE END USART1_Init 0 */

  /* USER CODE BEGIN USART1_Init 1 */

  /* USER CODE END USART1_Init 1 */
  huart1.Instance = USART1;
  huart1.Init.BaudRate = 115200;
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
  huart1.Init.StopBits = UART_STOPBITS_1;
  huart1.Init.Parity = UART_PARITY_NONE;
  huart1.Init.Mode = UART_MODE_TX_RX;
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART1_Init 2 */

  /* USER CODE END USART1_Init 2 */

}
/* USART2 init function */

void MX_USART2_UART_Init(void)
{

  /* USER CODE BEGIN USART2_Init 0 */

  /* USER CODE END USART2_Init 0 */

  /* USER CODE BEGIN USART2_Init 1 */

  /* USER CODE END USART2_Init 1 */
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 115200;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART2_Init 2 */

  /* USER CODE END USART2_Init 2 */

}
/* USART3 init function */

void MX_USART3_UART_Init(void)
{

  /* USER CODE BEGIN USART3_Init 0 */

  /* USER CODE END USART3_Init 0 */

  /* USER CODE BEGIN USART3_Init 1 */

  /* USER CODE END USART3_Init 1 */
  huart3.Instance = USART3;
  huart3.Init.BaudRate = 115200;
  huart3.Init.WordLength = UART_WORDLENGTH_8B;
  huart3.Init.StopBits = UART_STOPBITS_1;
  huart3.Init.Parity = UART_PARITY_NONE;
  huart3.Init.Mode = UART_MODE_TX_RX;
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart3) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART3_Init 2 */

  /* USER CODE END USART3_Init 2 */

}

void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(uartHandle->Instance==USART1)
  {
  /* USER CODE BEGIN USART1_MspInit 0 */

  /* USER CODE END USART1_MspInit 0 */
    /* USART1 clock enable */
    __HAL_RCC_USART1_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**USART1 GPIO Configuration
    PA9     ------> USART1_TX
    PA10     ------> USART1_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_9|GPIO_PIN_10;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /* USART1 DMA Init */
    /* USART1_RX Init */
    hdma_usart1_rx.Instance = DMA2_Stream2;
    hdma_usart1_rx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart1_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart1_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart1_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart1_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart1_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart1_rx.Init.Mode = DMA_NORMAL;
    hdma_usart1_rx.Init.Priority = DMA_PRIORITY_MEDIUM;
    hdma_usart1_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart1_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_usart1_rx);

    /* USART1_TX Init */
    hdma_usart1_tx.Instance = DMA2_Stream7;
    hdma_usart1_tx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart1_tx.Init.Direction = DMA_MEMORY_TO_PERIPH;
    hdma_usart1_tx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart1_tx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart1_tx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart1_tx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart1_tx.Init.Mode = DMA_NORMAL;
    hdma_usart1_tx.Init.Priority = DMA_PRIORITY_MEDIUM;
    hdma_usart1_tx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart1_tx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmatx,hdma_usart1_tx);

    /* USART1 interrupt Init */
    HAL_NVIC_SetPriority(USART1_IRQn, 5, 0);
    HAL_NVIC_EnableIRQ(USART1_IRQn);
  /* USER CODE BEGIN USART1_MspInit 1 */

  /* USER CODE END USART1_MspInit 1 */
  }
  else if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspInit 0 */

  /* USER CODE END USART2_MspInit 0 */
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**USART2 GPIO Configuration
    PA2     ------> USART2_TX
    PA3     ------> USART2_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /* USART2 DMA Init */
    /* USART2_RX Init */
    hdma_usart2_rx.Instance = DMA1_Stream5;
    hdma_usart2_rx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart2_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart2_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart2_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart2_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart2_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart2_rx.Init.Mode = DMA_NORMAL;
    hdma_usart2_rx.Init.Priority = DMA_PRIORITY_MEDIUM;
    hdma_usart2_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart2_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_usart2_rx);

    /* USART2_TX Init */
    hdma_usart2_tx.Instance = DMA1_Stream6;
    hdma_usart2_tx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart2_tx.Init.Direction = DMA_MEMORY_TO_PERIPH;
    hdma_usart2_tx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart2_tx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart2_tx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart2_tx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart2_tx.Init.Mode = DMA_NORMAL;
    hdma_usart2_tx.Init.Priority = DMA_PRIORITY_MEDIUM;
    hdma_usart2_tx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart2_tx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmatx,hdma_usart2_tx);

    /* USART2 interrupt Init */
    HAL_NVIC_SetPriority(USART2_IRQn, 5, 0);
    HAL_NVIC_EnableIRQ(USART2_IRQn);
  /* USER CODE BEGIN USART2_MspInit 1 */

  /* USER CODE END USART2_MspInit 1 */
  }
  else if(uartHandle->Instance==USART3)
  {
  /* USER CODE BEGIN USART3_MspInit 0 */

  /* USER CODE END USART3_MspInit 0 */
    /* USART3 clock enable */
    __HAL_RCC_USART3_CLK_ENABLE();

    __HAL_RCC_GPIOB_CLK_ENABLE();
    /**USART3 GPIO Configuration
    PB10     ------> USART3_TX
    PB11     ------> USART3_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_10|GPIO_PIN_11;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART3;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* USART3 DMA Init */
    /* USART3_RX Init */
    hdma_usart3_rx.Instance = DMA1_Stream1;
    hdma_usart3_rx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart3_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart3_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart3_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart3_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart3_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart3_rx.Init.Mode = DMA_NORMAL;
    hdma_usart3_rx.Init.Priority = DMA_PRIORITY_MEDIUM;
    hdma_usart3_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart3_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_usart3_rx);

    /* USART3_TX Init */
    hdma_usart3_tx.Instance = DMA1_Stream3;
    hdma_usart3_tx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart3_tx.Init.Direction = DMA_MEMORY_TO_PERIPH;
    hdma_usart3_tx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart3_tx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart3_tx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart3_tx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart3_tx.Init.Mode = DMA_NORMAL;
    hdma_usart3_tx.Init.Priority = DMA_PRIORITY_MEDIUM;
    hdma_usart3_tx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart3_tx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmatx,hdma_usart3_tx);

    /* USART3 interrupt Init */
    HAL_NVIC_SetPriority(USART3_IRQn, 5, 0);
    HAL_NVIC_EnableIRQ(USART3_IRQn);
  /* USER CODE BEGIN USART3_MspInit 1 */

  /* USER CODE END USART3_MspInit 1 */
  }
}

void HAL_UART_MspDeInit(UART_HandleTypeDef* uartHandle)
{

  if(uartHandle->Instance==USART1)
  {
  /* USER CODE BEGIN USART1_MspDeInit 0 */

  /* USER CODE END USART1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART1_CLK_DISABLE();

    /**USART1 GPIO Configuration
    PA9     ------> USART1_TX
    PA10     ------> USART1_RX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_9|GPIO_PIN_10);

    /* USART1 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);
    HAL_DMA_DeInit(uartHandle->hdmatx);

    /* USART1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART1_IRQn);
  /* USER CODE BEGIN USART1_MspDeInit 1 */

  /* USER CODE END USART1_MspDeInit 1 */
  }
  else if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspDeInit 0 */

  /* USER CODE END USART2_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART2_CLK_DISABLE();

    /**USART2 GPIO Configuration
    PA2     ------> USART2_TX
    PA3     ------> USART2_RX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_2|GPIO_PIN_3);

    /* USART2 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);
    HAL_DMA_DeInit(uartHandle->hdmatx);

    /* USART2 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART2_IRQn);
  /* USER CODE BEGIN USART2_MspDeInit 1 */

  /* USER CODE END USART2_MspDeInit 1 */
  }
  else if(uartHandle->Instance==USART3)
  {
  /* USER CODE BEGIN USART3_MspDeInit 0 */

  /* USER CODE END USART3_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART3_CLK_DISABLE();

    /**USART3 GPIO Configuration
    PB10     ------> USART3_TX
    PB11     ------> USART3_RX
    */
    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_10|GPIO_PIN_11);

    /* USART3 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);
    HAL_DMA_DeInit(uartHandle->hdmatx);

    /* USART3 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART3_IRQn);
  /* USER CODE BEGIN USART3_MspDeInit 1 */

  /* USER CODE END USART3_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */

void UartCallbackManager_vInit(void)
{
    UartCallbackManager_vClearCallbacks(UART_CALLBACK_IDLE_E);
    UartCallbackManager_vClearCallbacks(UART_CALLBACK_TX_COMPLETE_E);
    UartCallbackManager_vClearCallbacks(UART_CALLBACK_ERROR_E);

    psUartIdleCallbackList = NULL;
    psUartTxCompleteCallbackList = NULL;
    psUartErrorCallbackList = NULL;
}

void UartCallbackManager_vClearCallbacks(UART_CALLBACK_TYPE_E eCallbackType)
{
    if (eCallbackType >= UART_CALLBACK_TYPE_MAX_E)
    {
        return;
    }

    UART_CALLBACK_FUNCTION_NODE_T **ppListHead = NULL;

    switch (eCallbackType)
    {
      case UART_CALLBACK_IDLE_E:
        ppListHead = &psUartIdleCallbackList;
        break;
      case UART_CALLBACK_TX_COMPLETE_E:
        ppListHead = &psUartTxCompleteCallbackList;
        break;
      case UART_CALLBACK_ERROR_E:
        ppListHead = &psUartErrorCallbackList;
        break;
      default:
        return;
    }
    
    UART_CALLBACK_FUNCTION_NODE_T *psCurrentNode = *ppListHead;
    UART_CALLBACK_FUNCTION_NODE_T *psNextNode = NULL;

    while (psCurrentNode != NULL)
    {
        psNextNode = psCurrentNode->psNextNode;
        free(psCurrentNode);
        psCurrentNode = psNextNode;
    }
    
    *ppListHead = NULL;
}

void UartCallbackManager_vAddIdleCallback(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData)
{
  if (pvUartCallbackFunc == NULL || psTargetUart == NULL)
  {
      return;
  }
  
  UART_CALLBACK_FUNCTION_NODE_T *psNewNode = UartCallbackManager_psCreateNode(psTargetUart, pvUartCallbackFunc, pvCallbackData);
  if (psNewNode == NULL)
  {
      return;
  }

  if (psUartIdleCallbackList == NULL)
  {
    psUartIdleCallbackList = psNewNode;
  }
  else
  {
    UART_CALLBACK_FUNCTION_NODE_T *psCurrentNode = psUartIdleCallbackList;
    while (psCurrentNode->psNextNode != NULL)
    {
      psCurrentNode = psCurrentNode->psNextNode;
    }
    psCurrentNode->psNextNode = psNewNode;
  }
}

void UartCallbackManager_vAddTxCompleteCallback(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData)
{
  if (pvUartCallbackFunc == NULL || psTargetUart == NULL)
  {
      return;
  }
  
  UART_CALLBACK_FUNCTION_NODE_T *psNewNode = UartCallbackManager_psCreateNode(psTargetUart, pvUartCallbackFunc, pvCallbackData);
  if (psNewNode == NULL)
  {
      return;
  }

  if (psUartTxCompleteCallbackList == NULL)
  {
    psUartTxCompleteCallbackList = psNewNode;
  }
  else
  {
    UART_CALLBACK_FUNCTION_NODE_T *psCurrentNode = psUartTxCompleteCallbackList;
    while (psCurrentNode->psNextNode != NULL)
    {
      psCurrentNode = psCurrentNode->psNextNode;
    }
    psCurrentNode->psNextNode = psNewNode;
  }
}

void UartCallbackManager_vAddErrorCallback(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData)
{
  if (pvUartCallbackFunc == NULL || psTargetUart == NULL)
  {
      return;
  }
  
  UART_CALLBACK_FUNCTION_NODE_T *psNewNode = UartCallbackManager_psCreateNode(psTargetUart, pvUartCallbackFunc, pvCallbackData);
  if (psNewNode == NULL)
  {
      return;
  }

  if (psUartErrorCallbackList == NULL)
  {
    psUartErrorCallbackList = psNewNode;
  }
  else
  {
    UART_CALLBACK_FUNCTION_NODE_T *psCurrentNode = psUartErrorCallbackList;
    while (psCurrentNode->psNextNode != NULL)
    {
      psCurrentNode = psCurrentNode->psNextNode;
    }
    psCurrentNode->psNextNode = psNewNode;
  }
}

void UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_TYPE_E eCallbackType, UART_HandleTypeDef* huart)
{
    if (huart == NULL || eCallbackType >= UART_CALLBACK_TYPE_MAX_E)
    {
        return;
    }
    
    UART_CALLBACK_FUNCTION_NODE_T **ppListHead = NULL;

    switch (eCallbackType)
    {
      case UART_CALLBACK_IDLE_E:
        ppListHead = &psUartIdleCallbackList;
        break;
      case UART_CALLBACK_TX_COMPLETE_E:
        ppListHead = &psUartTxCompleteCallbackList;
        break;
      case UART_CALLBACK_ERROR_E:
        ppListHead = &psUartErrorCallbackList;
        break;
      default:
        return;
    }
    
    if (ppListHead == NULL || *ppListHead == NULL)
    {
        return;
    }
    
    UART_CALLBACK_FUNCTION_NODE_T *psCurrentNode = *ppListHead;
    
    while (psCurrentNode != NULL)
    {
        if (psCurrentNode->pvUartCallbackFunc != NULL && psCurrentNode->psTargetUart == huart)
        {
            psCurrentNode->pvUartCallbackFunc(psCurrentNode->pvCallbackData);
        }
        psCurrentNode = psCurrentNode->psNextNode;
    }
}

UART_CALLBACK_FUNCTION_NODE_T* UartCallbackManager_psCreateNode(UART_HandleTypeDef* psTargetUart, void (*pvUartCallbackFunc)(void* pvUserData), void* pvCallbackData)
{
  if (pvUartCallbackFunc == NULL || psTargetUart == NULL)
  {
      return NULL;
  }
  
  UART_CALLBACK_FUNCTION_NODE_T* psNewNode = (UART_CALLBACK_FUNCTION_NODE_T*)malloc(sizeof(UART_CALLBACK_FUNCTION_NODE_T));
  if (psNewNode == NULL)
  {
      return NULL;
  }
  
  psNewNode->psTargetUart = psTargetUart;
  psNewNode->pvUartCallbackFunc = pvUartCallbackFunc;
  psNewNode->pvCallbackData = pvCallbackData;
  psNewNode->psNextNode = NULL;
  
  return psNewNode;
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
  UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_IDLE_E, huart);
}

void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
  UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_TX_COMPLETE_E, huart);
}

void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
  UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_ERROR_E, huart);
}

/* USER CODE END 1 */
