<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>9</zoom_level>
  <element>
    <id>UMLObject</id>
    <coordinates>
      <x>54</x>
      <y>90</y>
      <w>279</w>
      <h>432</h>
    </coordinates>
    <panel_attributes>CircularBuffer_eInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>180</x>
      <y>126</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>72</x>
      <y>171</y>
      <w>243</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>检查参数有效性
(ps<PERSON>uff<PERSON>, pu8<PERSON>uffer<PERSON>em, u16Size)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>171</x>
      <y>225</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>99</x>
      <y>306</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>设置缓冲区参数
(pu8Buffer, u16Size)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>99</x>
      <y>378</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>初始化指针和计数器
(Head=0, Tail=0, Count=0)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>99</x>
      <y>450</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>设置初始化标志
(bInitialized = true)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>270</x>
      <y>234</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>180</x>
      <y>135</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>180</x>
      <y>198</y>
      <w>27</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;30.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>198</x>
      <y>225</y>
      <w>90</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Invalid]</panel_attributes>
    <additional_attributes>80.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>180</x>
      <y>252</y>
      <w>63</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid]</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>180</x>
      <y>333</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>180</x>
      <y>405</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLObject</id>
    <coordinates>
      <x>360</x>
      <y>90</y>
      <w>315</w>
      <h>594</h>
    </coordinates>
    <panel_attributes>CircularBuffer_ePutByte
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>504</x>
      <y>126</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>423</x>
      <y>171</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>检查初始化状态</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>495</x>
      <y>234</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>423</x>
      <y>297</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>检查缓冲区是否已满
(Count == Size)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>495</x>
      <y>378</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>423</x>
      <y>450</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>写入数据到Head位置
(Buffer[Head] = Data)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>423</x>
      <y>522</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>更新Head指针
(Head = (Head+1) % Size)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>423</x>
      <y>594</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>增加计数器
(Count++)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>594</x>
      <y>243</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>594</x>
      <y>387</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>135</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>198</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>522</x>
      <y>234</y>
      <w>90</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[No]</panel_attributes>
    <additional_attributes>80.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>261</y>
      <w>54</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Yes]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>324</y>
      <w>27</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>522</x>
      <y>378</y>
      <w>90</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Full]</panel_attributes>
    <additional_attributes>80.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>405</y>
      <w>72</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not Full]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>477</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>549</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLObject</id>
    <coordinates>
      <x>702</x>
      <y>90</y>
      <w>315</w>
      <h>594</h>
    </coordinates>
    <panel_attributes>CircularBuffer_eGetByte
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>846</x>
      <y>126</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>171</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>检查参数有效性
(psBuffer, pu8Data)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>837</x>
      <y>234</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>297</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>检查缓冲区是否为空
(Count == 0)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>837</x>
      <y>378</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>450</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>读取Tail位置数据
(*pu8Data = Buffer[Tail])</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>522</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>更新Tail指针
(Tail = (Tail+1) % Size)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>594</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>减少计数器
(Count--)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>954</x>
      <y>243</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>954</x>
      <y>387</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>846</x>
      <y>135</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>846</x>
      <y>198</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>864</x>
      <y>234</y>
      <w>108</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Invalid]</panel_attributes>
    <additional_attributes>100.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>846</x>
      <y>261</y>
      <w>63</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>846</x>
      <y>324</y>
      <w>27</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>864</x>
      <y>378</y>
      <w>108</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Empty]</panel_attributes>
    <additional_attributes>100.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>846</x>
      <y>405</y>
      <w>90</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not Empty]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>846</x>
      <y>477</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>846</x>
      <y>549</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
</diagram>
