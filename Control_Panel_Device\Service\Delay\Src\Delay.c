/**
 * @remark 版权所有(C)2025 TRIED.
 * @remark 本软件受版权法保护，未经授权禁止复制、分发或修改。
 * @file Delay.c
 * @brief DWT定时器精确延时实现文件
 * @details 基于ARM Cortex-M内核DWT定时器实现微秒级精确延时功能
 * <AUTHOR>
 * @date 2025-06-13
 * @note 适用于STM32F4系列微控制器
 */

/* Includes ------------------------------------------------------------------*/
#include "Delay.h"

/* Private define ------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/
static uint32_t Delay_u32CyclesPerUs = 0;      /* 每微秒的时钟周期数 */
static bool Delay_bInitialized = false;        /* 初始化标志 */

/* Private function prototypes -----------------------------------------------*/
static bool Delay_bEnableDWT(void);
static void Delay_vDisableDWT(void);
static uint32_t Delay_u32GetDWTCycles(void);
static void Delay_vDelayDWTCycles(uint32_t u32Cycles);

/* Private functions ---------------------------------------------------------*/
/**
 * @brief 使能DWT计数器
 * @retval bool true-成功，false-失败
 */
static bool Delay_bEnableDWT(void)
{
    /* 启用trace和debug模块 */
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    
    /* 重置计数器 */
    DWT->CYCCNT = 0;
    
    /* 启用CYCCNT */
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
    
    return true;
}

/**
 * @brief 禁用DWT计数器
 */
static void Delay_vDisableDWT(void)
{
    /* 禁用CYCCNT */
    DWT->CTRL &= ~DWT_CTRL_CYCCNTENA_Msk;
}

/**
 * @brief 获取当前DWT计数器值
 * @retval uint32_t 计数器值
 */
static uint32_t Delay_u32GetDWTCycles(void)
{
    return DWT->CYCCNT;
}

/**
 * @brief 基于DWT计数器的精确周期延时
 * @param u32Cycles 延时周期数
 */
static void Delay_vDelayDWTCycles(uint32_t u32Cycles)
{
    if (u32Cycles == 0) {
        return;
    }
    
    uint32_t u32StartCycles = Delay_u32GetDWTCycles();
    uint32_t u32ElapsedCycles;
    
    do {
        uint32_t u32CurrentCycles = Delay_u32GetDWTCycles();
        if (u32CurrentCycles >= u32StartCycles) {
            u32ElapsedCycles = u32CurrentCycles - u32StartCycles;
        } else {
            /* 处理计数器溢出情况 */
            u32ElapsedCycles = (0xFFFFFFFFUL - u32StartCycles) + u32CurrentCycles + 1;
        }
    } while (u32ElapsedCycles < u32Cycles);
}

/* Exported functions --------------------------------------------------------*/
/**
 * @brief 初始化DWT延时模块
 * @param u32SystemCoreClock 系统时钟频率（Hz）
 * @retval bool true-初始化成功，false-初始化失败
 */
bool Delay_bInit(uint32_t u32SystemCoreClock)
{
    if (u32SystemCoreClock == 0) {
        return false;
    }
    
    if (Delay_bInitialized) {
        return true;
    }
    
    /* 计算每微秒的时钟周期数 */
    Delay_u32CyclesPerUs = u32SystemCoreClock / 1000000UL;
    if (Delay_u32CyclesPerUs == 0) {
        return false;
    }
    
    /* 启用DWT计数器 */
    if (!Delay_bEnableDWT()) {
        return false;
    }
    
    Delay_bInitialized = true;
    
    return true;
}

/**
 * @brief 反初始化DWT延时模块
 */
void Delay_vDeInit(void)
{
    if (Delay_bInitialized) {
        Delay_vDisableDWT();
        Delay_u32CyclesPerUs = 0;
        Delay_bInitialized = false;
    }
}

/**
 * @brief 微秒级精确延时
 * @param u32MicroSeconds 延时时间（微秒）
 */
void Delay_vDelayUs(uint32_t u32MicroSeconds)
{
    if (!Delay_bInitialized || u32MicroSeconds == 0) {
        return;
    }
    
    if (u32MicroSeconds > DELAY_MAX_US) {
        u32MicroSeconds = DELAY_MAX_US;
    }
    
    uint32_t u32Cycles = u32MicroSeconds * Delay_u32CyclesPerUs;
    Delay_vDelayDWTCycles(u32Cycles);
}

/**
 * @brief 毫秒级延时
 * @param u32MilliSeconds 延时时间（毫秒）
 */
void Delay_vDelayMs(uint32_t u32MilliSeconds)
{
    if (!Delay_bInitialized || u32MilliSeconds == 0) {
        return;
    }
    
    if (u32MilliSeconds > DELAY_MAX_MS) {
        u32MilliSeconds = DELAY_MAX_MS;
    }
    
    /* 通过多次微秒延时实现毫秒延时 */
    for (uint32_t i = 0; i < u32MilliSeconds; i++) {
        Delay_vDelayUs(1000);
    }
}

/**
 * @brief 获取当前DWT计数器值
 * @retval uint32_t 当前计数器值
 */
uint32_t Delay_u32GetCounter(void)
{
    if (!Delay_bInitialized) {
        return 0;
    }
    
    return Delay_u32GetDWTCycles();
}

/**
 * @brief 计算两个计数器值之间的时间差（微秒）
 * @param u32StartCounter 起始计数器值
 * @param u32EndCounter 结束计数器值
 * @retval uint32_t 时间差（微秒）
 */
uint32_t Delay_u32GetTimeDiffUs(uint32_t u32StartCounter, uint32_t u32EndCounter)
{
    if (!Delay_bInitialized || Delay_u32CyclesPerUs == 0) {
        return 0;
    }
    
    uint32_t u32CycleDiff;
    
    if (u32EndCounter >= u32StartCounter) {
        u32CycleDiff = u32EndCounter - u32StartCounter;
    } else {
        /* 处理计数器溢出情况 */
        u32CycleDiff = (0xFFFFFFFFUL - u32StartCounter) + u32EndCounter + 1;
    }
    
    return u32CycleDiff / Delay_u32CyclesPerUs;
}
