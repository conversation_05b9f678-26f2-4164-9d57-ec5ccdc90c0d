<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>6</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>276</x>
      <y>216</y>
      <w>552</w>
      <h>186</h>
    </coordinates>
    <panel_attributes>HalUi
--
+HalUi_eInit(psConfig:const HALUI_CONFIG_T* const):HALUI_STATUS_E
+HalUi_vDeInit(void):void
+HalUi_eGetDefaultConfig(psConfig:HALUI_CONFIG_T* const):HALUI_STATUS_E
+HalUi_eLcdIoInit(void):HALUI_STATUS_E
+HalUi_eCreateLcdDisplay(void):HALUI_STATUS_E
+HalUi_eIsLcdBusy(pbBusy:BOOL* const):HALUI_STATUS_E
+HalUi_eWaitLcdReady(u32TimeoutMs:const U32):HALUI_STATUS_E
+HalUi_eGetLcdDisplay(ppsLcdDisplay:lv_display_t** const):HALUI_STATUS_E
+HalUi_vLcdColorTransferReadyCallback(psSpiHandle:SPI_HandleTypeDef*):void
+HalUi_vLcdSendCommand(psDisp:lv_display_t*, pu8Cmd:const uint8_t*, szCmdSize:size_t, pu8Param:const uint8_t*, szParamSize:size_t):void
+HalUi_vLcdSendColor(psDisp:lv_display_t*, pu8Cmd:const uint8_t*, szCmdSize:size_t, pu8Param:uint8_t*, szParamSize:size_t):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>54</x>
      <y>222</y>
      <w>168</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALUI_STATUS_E
--
HALUI_OK_E = 0
HALUI_ERROR_E
HALUI_TIMEOUT_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>0</x>
      <y>294</y>
      <w>222</w>
      <h>108</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
HALUI_CONFIG_T
--
+psSpiHandle:SPI_HandleTypeDef*
+u16LcdCsPin:U16
+psLcdCsPort:GPIO_TypeDef*
+u16LcdDcxPin:U16
+psLcdDcxPort:GPIO_TypeDef*
+u16LcdResetPin:U16
+psLcdResetPort:GPIO_TypeDef*
+sLcdDisplayConfig:HALUI_LCD_DISPLAY_CONFIG_T</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>222</x>
      <y>48</y>
      <w>660</w>
      <h>126</h>
    </coordinates>
    <panel_attributes>LVGL
--
+lv_st7789_create(hor_res:uint32_t, ver_res:uint32_t, flags:lv_lcd_flag_t, send_cmd_cb:lv_st7789_send_cmd_cb_t, send_color_cb:lv_st7789_send_color_cb_t):lv_display_t*
+lv_display_set_rotation(disp:lv_display_t*, rotation:lv_display_rotation_t):void
+lv_st7789_set_invert(disp:lv_display_t*, invert:bool):void
+lv_display_set_antialiasing(disp:lv_display_t*, en:bool):void
+lv_display_set_buffers(disp:lv_display_t*, buf1:void*, buf2:void*, buf_size:uint32_t, render_mode:lv_display_render_mode_t):void
+lv_display_flush_ready(disp:lv_display_t*):void
+lv_malloc(size:size_t):void*
+lv_free(data:void*):void
+lv_color_format_get_size(cf:lv_color_format_t):uint8_t
+lv_display_get_color_format(disp:lv_display_t*):lv_color_format_t</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>258</x>
      <y>444</y>
      <w>594</w>
      <h>78</h>
    </coordinates>
    <panel_attributes>STM32 HAL
--
+HAL_GPIO_WritePin(GPIOx:GPIO_TypeDef*, GPIO_Pin:uint16_t, PinState:GPIO_PinState):void
+HAL_SPI_Init(hspi:SPI_HandleTypeDef*):HAL_StatusTypeDef
+HAL_SPI_Transmit(hspi:SPI_HandleTypeDef*, pData:const uint8_t*, Size:uint16_t, Timeout:uint32_t):HAL_StatusTypeDef
+HAL_SPI_Transmit_DMA(hspi:SPI_HandleTypeDef*, pData:const uint8_t*, Size:uint16_t):HAL_StatusTypeDef 
+HAL_SPI_RegisterCallback(hspi:SPI_HandleTypeDef*, CallbackID:HAL_SPI_CallbackIDTypeDef, pCallback:pSPI_CallbackTypeDef):HAL_StatusTypeDef</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>216</x>
      <y>222</y>
      <w>72</w>
      <h>24</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;20.0;100.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>216</x>
      <y>294</y>
      <w>72</w>
      <h>24</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;20.0;100.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>540</x>
      <y>168</y>
      <w>42</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;80.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>540</x>
      <y>396</y>
      <w>42</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>882</x>
      <y>294</y>
      <w>192</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>FreeRTOS
--
+osDelay(millisec:uint32_t):osStatus 
+xTaskGetTickCount(void):TickType_t</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>288</y>
      <w>72</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>100.0;20.0;10.0;20.0</additional_attributes>
  </element>
</diagram>
