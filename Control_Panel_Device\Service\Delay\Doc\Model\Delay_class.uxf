<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>7</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>371</x>
      <y>357</y>
      <w>434</w>
      <h>252</h>
    </coordinates>
    <panel_attributes>Delay
&lt;&lt;Module&gt;&gt;
--
-Delay_u32CyclesPerUs: uint32_t
-Delay_bInitialized: bool
--
/+Delay_bInit(u32SystemCoreClock: uint32_t): bool/
/+Delay_vDeInit(void): void/
/+Delay_vDelayUs(u32MicroSeconds: uint32_t): void/
/+Delay_vDelayMs(u32MilliSeconds: uint32_t): void/
/+Delay_u32GetCounter(void): uint32_t/
/+Delay_u32GetTimeDiffUs(u32StartCounter: uint32_t, u32EndCounter: uint32_t): uint32_t/
/-Delay_bEnableDWT(void): bool/
/-Delay_vDisableDWT(void): void/
/-Delay_u32GetDWTCycles(void): uint32_t/
/-Delay_vDelayDWTCycles(u32Cycles: uint32_t): void/
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>448</x>
      <y>161</y>
      <w>280</w>
      <h>133</h>
    </coordinates>
    <panel_attributes>"ARM Cortex-M Core" as CORE
&lt;&lt;Hardware&gt;&gt;
--
DWT: Data Watchpoint and Trace Unit
CoreDebug: Core Debug
--
DWT-&gt;CYCCNT: uint32_t
DWT-&gt;CTRL: uint32_t
CoreDebug-&gt;DEMCR: uint32_t
DWT_CTRL_CYCCNTENA_Msk
CoreDebug_DEMCR_TRCENA_Msk</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>574</x>
      <y>287</y>
      <w>42</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>lt=&lt;..
uses</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;100.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>91</x>
      <y>413</y>
      <w>210</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>"DELAY Constants" as CONSTANTS
&lt;&lt;Defines&gt;&gt;
--
DELAY_MAX_US: 1000000UL
DELAY_MAX_MS: 1000UL</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>294</x>
      <y>448</y>
      <w>91</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;..
uses</panel_attributes>
    <additional_attributes>110.0;20.0;10.0;20.0</additional_attributes>
  </element>
</diagram>
