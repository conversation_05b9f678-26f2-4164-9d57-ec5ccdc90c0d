//----------------------------------------------------------------------------
/**
* @file Hal<PERSON>ey.h
* @remark <PERSON><PERSON><PERSON> public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef HALKEY_H_
#define HALKEY_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "main.h"
#include "GlobalTypes.h"
#include "Utility.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------
#define HALKEY_MATRIX_ROWS_D                      4U                                                /* Number of key matrix rows (input lines) */
#define HALKEY_MATRIX_COLS_D                      4U                                                /* Number of key matrix columns (output lines) */
#define HALKEY_MATRIX_TOTAL_KEYS_D                (HALKEY_MATRIX_ROWS_D * HALKEY_MATRIX_COLS_D)     /* Total number of keys in matrix (16 keys) */

/**
* @brief HAL layer key module status enumeration
*/
typedef enum
{
    HALKEY_OK_E = 0U,               /* Operation successful */
    HALKEY_ERROR_E                  /* Operation failed/error */
} HALKEY_STATUS_E;

/**
* @brief GPIO pin configuration structure
* @details Used to define GPIO pin configuration for key matrix
*/
typedef struct
{
    GPIO_TypeDef* psGpioPort;       /* GPIO port pointer (such as GPIOA, GPIOB, etc.) */
    U16 u16GpioPin;                 /* GPIO pin number (such as GPIO_PIN_0, GPIO_PIN_1, etc.) */
} HALKEY_GPIO_PIN_T;

/**
* @brief HAL layer key configuration structure
* @details Contains all configuration parameters for key matrix
*/
typedef struct
{
    HALKEY_GPIO_PIN_T asInputPins[HALKEY_MATRIX_ROWS_D];    /* Input pin configuration array (row lines) */
    HALKEY_GPIO_PIN_T asOutputPins[HALKEY_MATRIX_COLS_D];   /* Output pin configuration array (column lines) */
    U32 u32ScanDelayUs;                                     /* Key scan delay time (microseconds) */
} HALKEY_CONFIG_T;

/**
* @brief HAL layer key handle structure
* @details Contains configuration parameters, status information, etc. of key module
*/
typedef struct
{
    HALKEY_CONFIG_T sConfig;        /* Configuration parameter structure */
    BOOL bInitialized;              /* Initialization flag */
    U16 u16LastKeyMatrix;           /* Last scanned key matrix status */
} HALKEY_HANDLE_T;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
HALKEY_STATUS_E HalKey_eInit(HALKEY_HANDLE_T* const psHandle, const HALKEY_CONFIG_T* const psConfig);
HALKEY_STATUS_E HalKey_eDeInit(HALKEY_HANDLE_T* const psHandle);
HALKEY_STATUS_E HalKey_eGetDefaultConfig(HALKEY_CONFIG_T* const psConfig);
HALKEY_STATUS_E HalKey_eScanMatrix(HALKEY_HANDLE_T* const psHandle, U16* const pu16KeyMatrix);
HALKEY_STATUS_E HalKey_eSetOutputLine(const HALKEY_HANDLE_T* const psHandle, const U8 u8OutputLine, const BOOL bState);
HALKEY_STATUS_E HalKey_eReadInputLine(const HALKEY_HANDLE_T* const psHandle, const U8 u8InputLine, BOOL* const pbState);
HALKEY_STATUS_E HalKey_eGetLastKeyMatrix(const HALKEY_HANDLE_T* const psHandle, U16* const pu16KeyMatrix);

#endif /* HALKEY_H_ */

//===========================================================================
// End of file.
//===========================================================================








