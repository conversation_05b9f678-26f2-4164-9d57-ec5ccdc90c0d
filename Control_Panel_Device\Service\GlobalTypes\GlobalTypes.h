//----------------------------------------------------------------------------
/**
* @file GlobalTypes.h
* @remark Global data types definition.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef GLOBALTYPES_H_
#define GLOBALTYPES_H_

//-----------------------------------------------------------------------
// Includes:
//-----------------------------------------------------------------------
#include <stdbool.h>
#include <stdlib.h>

//------------------------------------------------------------------------
// Public Definitions:
//------------------------------------------------------------------------
typedef char                CHAR;
typedef signed char         S8;
typedef signed short        S16;
typedef signed int          S32;
typedef signed long long    S64;
typedef unsigned char       U8;
typedef unsigned short      U16;
typedef unsigned int        U32;
typedef unsigned long long  U64;
typedef float               F32;
typedef double              F64;
typedef _Bool               BOOL;

#define NULL_D              ((void *)0)
#define FALSE_D             0
#define TRUE_D              !FALSE_D

//-----------------------------------------------------------------------
// Public Function Prototypes:
//-----------------------------------------------------------------------

#endif /* GLOBALTYPES_H_ */


//===========================================================================
// End of file.
//===========================================================================








