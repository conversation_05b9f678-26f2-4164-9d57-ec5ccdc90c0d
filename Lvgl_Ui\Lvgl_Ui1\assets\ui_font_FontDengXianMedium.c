/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: --bpp 4 --size 15 --font F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/STM32CubeIDE_FreeRTOS_LVGL_UI/assets/Deng.ttf -o F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/STM32CubeIDE_FreeRTOS_LVGL_UI/assets\ui_font_FontDengXianMedium.c --format lvgl -r 0x20-0x7f --symbols 设备本地远程菜单名称状态自定义参数设定频率电机实际输出电流→返回确认编辑输出频率转矩编辑主页视图选项点击“”添加新完成参数显示风格位数名称最小值 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_FONTDENGXIANMEDIUM
#define UI_FONT_FONTDENGXIANMEDIUM 1
#endif

#if UI_FONT_FONTDENGXIANMEDIUM

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x5a, 0x5a, 0x59, 0x49, 0x49, 0x48, 0x48, 0x14,
    0x0, 0x11, 0x6a,

    /* U+0022 "\"" */
    0xf, 0x8, 0x60, 0xd0, 0x76, 0x8, 0x4, 0x30,

    /* U+0023 "#" */
    0x0, 0xa, 0x0, 0x36, 0x0, 0x0, 0xa0, 0x6,
    0x30, 0x0, 0x27, 0x0, 0x90, 0x2, 0xac, 0xba,
    0xad, 0xa4, 0x0, 0x81, 0x0, 0x90, 0x0, 0xa,
    0x0, 0x36, 0x0, 0x0, 0xa0, 0x5, 0x40, 0x6,
    0xad, 0xaa, 0xda, 0xa0, 0x4, 0x50, 0xa, 0x0,
    0x0, 0x72, 0x0, 0x90, 0x0, 0xa, 0x0, 0x27,
    0x0, 0x0,

    /* U+0024 "$" */
    0x2, 0xbd, 0xdb, 0x20, 0xc, 0x35, 0x53, 0xd0,
    0xe, 0x5, 0x50, 0x70, 0xe, 0x15, 0x50, 0x0,
    0x6, 0xd9, 0x50, 0x0, 0x0, 0x3b, 0xfa, 0x10,
    0x0, 0x5, 0x56, 0xd0, 0x0, 0x5, 0x50, 0xc2,
    0x37, 0x5, 0x50, 0xb3, 0x1d, 0x25, 0x51, 0xd0,
    0x3, 0xcd, 0xdb, 0x30, 0x0, 0x5, 0x50, 0x0,

    /* U+0025 "%" */
    0x19, 0x60, 0xa, 0x0, 0x71, 0x90, 0x18, 0x0,
    0x90, 0x90, 0x72, 0x0, 0xa0, 0x90, 0xa0, 0x0,
    0x90, 0x93, 0x79, 0x50, 0x62, 0x99, 0x64, 0x90,
    0xa, 0x59, 0x72, 0x81, 0x0, 0x54, 0x81, 0x81,
    0x0, 0x90, 0x81, 0x81, 0x1, 0x80, 0x63, 0x90,
    0x7, 0x20, 0x1a, 0x70,

    /* U+0026 "&" */
    0x0, 0x7, 0xbb, 0x90, 0x0, 0x0, 0x3, 0xa0,
    0x9, 0x50, 0x0, 0x0, 0x58, 0x0, 0x86, 0x0,
    0x0, 0x2, 0xd0, 0x5e, 0x10, 0x0, 0x0, 0xb,
    0xeb, 0x20, 0x2, 0x0, 0x1a, 0xde, 0x20, 0x0,
    0xe0, 0xc, 0x80, 0x5d, 0x10, 0x49, 0x2, 0xe0,
    0x0, 0x6c, 0xc, 0x20, 0x2c, 0x0, 0x0, 0x8d,
    0x90, 0x0, 0xd5, 0x0, 0x19, 0xdb, 0x10, 0x1,
    0xbe, 0xdc, 0x60, 0x6e, 0xa0,

    /* U+0027 "'" */
    0xd2, 0xc1, 0x80,

    /* U+0028 "(" */
    0x0, 0x3a, 0x0, 0xc, 0x10, 0x6, 0x70, 0x0,
    0xc1, 0x0, 0x1c, 0x0, 0x2, 0xa0, 0x0, 0x39,
    0x0, 0x2, 0xa0, 0x0, 0x1c, 0x0, 0x0, 0xc0,
    0x0, 0x6, 0x70, 0x0, 0xc, 0x10, 0x0, 0x3a,
    0x0,

    /* U+0029 ")" */
    0x49, 0x0, 0xa, 0x30, 0x2, 0xb0, 0x0, 0xb2,
    0x0, 0x66, 0x0, 0x48, 0x0, 0x39, 0x0, 0x48,
    0x0, 0x66, 0x0, 0xa2, 0x1, 0xb0, 0x9, 0x30,
    0x49, 0x0,

    /* U+002A "*" */
    0x0, 0x47, 0x0, 0x35, 0x47, 0x34, 0x16, 0xcd,
    0x72, 0x1, 0xa9, 0x20, 0x7, 0x21, 0x90,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x6, 0x60, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x6c, 0xcd, 0xdc, 0xc6, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x6, 0x60, 0x0, 0x0, 0x6, 0x60, 0x0,

    /* U+002C "," */
    0x30, 0xd2, 0x71, 0x40,

    /* U+002D "-" */
    0x5d, 0xdd, 0xb0,

    /* U+002E "." */
    0x30, 0xe2,

    /* U+002F "/" */
    0x0, 0x0, 0x86, 0x0, 0x0, 0xd0, 0x0, 0x5,
    0x90, 0x0, 0xc, 0x20, 0x0, 0x3b, 0x0, 0x0,
    0xa4, 0x0, 0x1, 0xd0, 0x0, 0x7, 0x70, 0x0,
    0xd, 0x10, 0x0, 0x59, 0x0, 0x0, 0xb3, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x9c, 0xc9, 0x0, 0x8, 0x70, 0x8, 0x80,
    0xe, 0x0, 0x0, 0xe0, 0x2c, 0x0, 0x0, 0xc2,
    0x4a, 0x0, 0x0, 0xb3, 0x4a, 0x0, 0x0, 0xa4,
    0x4a, 0x0, 0x0, 0xb3, 0x2c, 0x0, 0x0, 0xc1,
    0xe, 0x0, 0x1, 0xd0, 0x8, 0x80, 0x9, 0x70,
    0x0, 0x9c, 0xc8, 0x0,

    /* U+0031 "1" */
    0x1, 0xbb, 0x0, 0x3c, 0x7b, 0x0, 0x40, 0x3b,
    0x0, 0x0, 0x3b, 0x0, 0x0, 0x3b, 0x0, 0x0,
    0x3b, 0x0, 0x0, 0x3b, 0x0, 0x0, 0x3b, 0x0,
    0x0, 0x3b, 0x0, 0x0, 0x3b, 0x0, 0xac, 0xde,
    0xcb,

    /* U+0032 "2" */
    0x1, 0xac, 0xc9, 0x0, 0xa, 0x70, 0x8, 0x90,
    0xb, 0x0, 0x1, 0xd0, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0x0, 0x8, 0x70, 0x0, 0x0, 0x5d, 0x0,
    0x0, 0x5, 0xd1, 0x0, 0x0, 0x4d, 0x10, 0x0,
    0x2, 0xd1, 0x0, 0x0, 0xc, 0x40, 0x0, 0x0,
    0x2f, 0xdd, 0xdd, 0xd0,

    /* U+0033 "3" */
    0x1, 0xbc, 0xc9, 0x0, 0xc, 0x40, 0x9, 0x70,
    0x9, 0x0, 0x3, 0xb0, 0x0, 0x0, 0x3, 0xb0,
    0x0, 0x0, 0x2c, 0x30, 0x0, 0x2e, 0xf6, 0x0,
    0x0, 0x0, 0x19, 0x80, 0x0, 0x0, 0x0, 0xe0,
    0x38, 0x0, 0x0, 0xe0, 0xe, 0x30, 0x6, 0xa0,
    0x3, 0xbc, 0xca, 0x10,

    /* U+0034 "4" */
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0xce, 0x0,
    0x0, 0x8, 0x6e, 0x0, 0x0, 0x1c, 0xe, 0x0,
    0x0, 0xa4, 0xe, 0x0, 0x3, 0xb0, 0xe, 0x0,
    0xb, 0x20, 0xe, 0x0, 0x49, 0x0, 0xe, 0x0,
    0x9c, 0xcc, 0xcf, 0xc3, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x0, 0xe, 0x0,

    /* U+0035 "5" */
    0x8, 0xdc, 0xcc, 0x70, 0x9, 0x40, 0x0, 0x0,
    0xa, 0x30, 0x0, 0x0, 0xb, 0x20, 0x0, 0x0,
    0xc, 0x8c, 0xc8, 0x0, 0x9, 0x60, 0x8, 0x90,
    0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0xd1,
    0x6, 0x0, 0x0, 0xe0, 0xc, 0x40, 0x8, 0x90,
    0x2, 0xbc, 0xc8, 0x0,

    /* U+0036 "6" */
    0x0, 0x6c, 0xcb, 0x10, 0x4, 0xb0, 0x5, 0xb0,
    0xb, 0x30, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0x1d, 0x4b, 0xc8, 0x0, 0x2e, 0x90, 0x8, 0x90,
    0x1f, 0x0, 0x0, 0xe0, 0xe, 0x0, 0x0, 0xd1,
    0xc, 0x20, 0x0, 0xe0, 0x6, 0xa0, 0x7, 0x90,
    0x0, 0x7d, 0xc9, 0x0,

    /* U+0037 "7" */
    0x2d, 0xdd, 0xdd, 0xf1, 0x0, 0x0, 0x2, 0xb0,
    0x0, 0x0, 0xb, 0x30, 0x0, 0x0, 0x3a, 0x0,
    0x0, 0x0, 0xa3, 0x0, 0x0, 0x1, 0xc0, 0x0,
    0x0, 0x7, 0x70, 0x0, 0x0, 0xb, 0x30, 0x0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0x1d, 0x0, 0x0,
    0x0, 0x3b, 0x0, 0x0,

    /* U+0038 "8" */
    0x1, 0xab, 0xb9, 0x0, 0xa, 0x50, 0x7, 0x80,
    0xe, 0x0, 0x1, 0xd0, 0xe, 0x0, 0x2, 0xd0,
    0x8, 0x80, 0x9, 0x60, 0x0, 0xce, 0xea, 0x0,
    0xb, 0x50, 0x6, 0xa0, 0x2c, 0x0, 0x0, 0xd1,
    0x2c, 0x0, 0x0, 0xe1, 0xd, 0x30, 0x5, 0xc0,
    0x2, 0xbb, 0xba, 0x10,

    /* U+0039 "9" */
    0x1, 0xac, 0xd7, 0x0, 0xb, 0x50, 0xb, 0x40,
    0x1d, 0x0, 0x2, 0xb0, 0x3b, 0x0, 0x0, 0xe0,
    0x1d, 0x0, 0x1, 0xf0, 0xa, 0x70, 0xa, 0xf0,
    0x0, 0x9c, 0xb3, 0xe0, 0x0, 0x0, 0x0, 0xd0,
    0x1, 0x0, 0x5, 0x90, 0xd, 0x30, 0x1c, 0x20,
    0x3, 0xcc, 0xc4, 0x0,

    /* U+003A ":" */
    0xe2, 0x30, 0x0, 0x0, 0x0, 0x0, 0x30, 0xe2,

    /* U+003B ";" */
    0xe3, 0x30, 0x0, 0x0, 0x0, 0x0, 0x30, 0xe2,
    0x71, 0x50,

    /* U+003C "<" */
    0x0, 0x0, 0x1, 0x86, 0x0, 0x3, 0xab, 0x40,
    0x4, 0xba, 0x30, 0x0, 0x7b, 0x10, 0x0, 0x0,
    0x2a, 0xb4, 0x0, 0x0, 0x0, 0x18, 0xc6, 0x0,
    0x0, 0x0, 0x7, 0xc4, 0x0, 0x0, 0x0, 0x1,

    /* U+003D "=" */
    0x6c, 0xcc, 0xcc, 0xc6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xcc, 0xcc, 0xc6,

    /* U+003E ">" */
    0x68, 0x10, 0x0, 0x0, 0x4, 0xba, 0x30, 0x0,
    0x0, 0x3, 0xab, 0x40, 0x0, 0x0, 0x1, 0xb7,
    0x0, 0x0, 0x4b, 0xa2, 0x0, 0x6c, 0x81, 0x0,
    0x4c, 0x70, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x6, 0xcc, 0xb2, 0x4, 0xc0, 0x3, 0xd0, 0x94,
    0x0, 0xc, 0x20, 0x0, 0x0, 0xd1, 0x0, 0x0,
    0x5b, 0x0, 0x0, 0x5d, 0x10, 0x0, 0x2d, 0x10,
    0x0, 0x8, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x10, 0x0, 0x0, 0xa6, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x39, 0xbb, 0xbb, 0x70, 0x0, 0x0,
    0x8, 0xa2, 0x0, 0x0, 0x6c, 0x10, 0x0, 0x96,
    0x0, 0x0, 0x0, 0x3, 0xb0, 0x4, 0xa0, 0x6,
    0xbb, 0xa4, 0xa0, 0xa3, 0xb, 0x20, 0x79, 0x0,
    0x1d, 0x60, 0x66, 0xc, 0x0, 0xd0, 0x0, 0xc,
    0x20, 0x57, 0x2a, 0x4, 0xa0, 0x0, 0xe, 0x0,
    0x75, 0x3a, 0x4, 0x90, 0x0, 0x6b, 0x0, 0xc1,
    0x1b, 0x1, 0xd1, 0x4, 0x9a, 0x7, 0x80, 0xc,
    0x10, 0x4b, 0xa6, 0x9, 0xb7, 0x0, 0x4, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0x30,
    0x0, 0x16, 0xc2, 0x0, 0x0, 0x2, 0x8b, 0xbb,
    0xa6, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xf8, 0x0, 0x0, 0x0, 0x5, 0xae,
    0x0, 0x0, 0x0, 0xb, 0x4b, 0x40, 0x0, 0x0,
    0x1e, 0x6, 0xa0, 0x0, 0x0, 0x79, 0x1, 0xe0,
    0x0, 0x0, 0xd4, 0x0, 0xb5, 0x0, 0x2, 0xe0,
    0x0, 0x6b, 0x0, 0x8, 0xed, 0xdd, 0xdf, 0x10,
    0xe, 0x30, 0x0, 0xa, 0x70, 0x4d, 0x0, 0x0,
    0x4, 0xd0, 0xa6, 0x0, 0x0, 0x0, 0xd3,

    /* U+0042 "B" */
    0xad, 0xcc, 0xb3, 0xa, 0x50, 0x4, 0xe0, 0xa5,
    0x0, 0xc, 0x3a, 0x50, 0x0, 0xd2, 0xa5, 0x0,
    0x7b, 0xa, 0xdc, 0xed, 0x30, 0xa5, 0x0, 0x2c,
    0x5a, 0x50, 0x0, 0x3c, 0xa5, 0x0, 0x3, 0xca,
    0x50, 0x1, 0xb7, 0xad, 0xcc, 0xc6, 0x0,

    /* U+0043 "C" */
    0x0, 0x1a, 0xdd, 0xc5, 0x0, 0x1d, 0x60, 0x1,
    0xc6, 0x8, 0x90, 0x0, 0x2, 0x60, 0xe2, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x1, 0xe0,
    0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0xd2, 0x0, 0x0, 0x0, 0x8, 0x90, 0x0, 0x2,
    0x80, 0x1d, 0x50, 0x2, 0xc5, 0x0, 0x1a, 0xdd,
    0xc4, 0x0,

    /* U+0044 "D" */
    0xae, 0xdd, 0xd9, 0x20, 0xa, 0x50, 0x0, 0x5e,
    0x30, 0xa5, 0x0, 0x0, 0x3d, 0xa, 0x50, 0x0,
    0x0, 0xc3, 0xa5, 0x0, 0x0, 0x9, 0x6a, 0x50,
    0x0, 0x0, 0x87, 0xa5, 0x0, 0x0, 0xa, 0x5a,
    0x50, 0x0, 0x0, 0xd2, 0xa5, 0x0, 0x0, 0x5c,
    0xa, 0x50, 0x0, 0x5d, 0x20, 0xae, 0xdd, 0xd9,
    0x10, 0x0,

    /* U+0045 "E" */
    0xae, 0xdd, 0xd9, 0xa, 0x50, 0x0, 0x0, 0xa5,
    0x0, 0x0, 0xa, 0x50, 0x0, 0x0, 0xa5, 0x0,
    0x0, 0xa, 0xed, 0xdd, 0x30, 0xa5, 0x0, 0x0,
    0xa, 0x50, 0x0, 0x0, 0xa5, 0x0, 0x0, 0xa,
    0x50, 0x0, 0x0, 0xae, 0xdd, 0xdd, 0x0,

    /* U+0046 "F" */
    0xae, 0xdd, 0xda, 0xa5, 0x0, 0x0, 0xa5, 0x0,
    0x0, 0xa5, 0x0, 0x0, 0xa5, 0x0, 0x0, 0xae,
    0xdd, 0xd6, 0xa5, 0x0, 0x0, 0xa5, 0x0, 0x0,
    0xa5, 0x0, 0x0, 0xa5, 0x0, 0x0, 0xa5, 0x0,
    0x0,

    /* U+0047 "G" */
    0x0, 0x2a, 0xdd, 0xc6, 0x0, 0x1, 0xe5, 0x0,
    0x1c, 0x70, 0xa, 0x70, 0x0, 0x1, 0xa0, 0xf,
    0x10, 0x0, 0x0, 0x0, 0x1e, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x6, 0xcc, 0xd2, 0xe, 0x20, 0x0, 0x0, 0xc2,
    0x9, 0x90, 0x0, 0x0, 0xc2, 0x1, 0xd6, 0x0,
    0x5, 0xd1, 0x0, 0x19, 0xdd, 0xd8, 0x10,

    /* U+0048 "H" */
    0xa5, 0x0, 0x0, 0xe, 0xa, 0x50, 0x0, 0x0,
    0xe0, 0xa5, 0x0, 0x0, 0xe, 0xa, 0x50, 0x0,
    0x0, 0xe0, 0xa5, 0x0, 0x0, 0xe, 0xa, 0xed,
    0xdd, 0xdd, 0xf0, 0xa5, 0x0, 0x0, 0xe, 0xa,
    0x50, 0x0, 0x0, 0xe0, 0xa5, 0x0, 0x0, 0xe,
    0xa, 0x50, 0x0, 0x0, 0xe0, 0xa5, 0x0, 0x0,
    0xe, 0x0,

    /* U+0049 "I" */
    0xa5, 0xa5, 0xa5, 0xa5, 0xa5, 0xa5, 0xa5, 0xa5,
    0xa5, 0xa5, 0xa5,

    /* U+004A "J" */
    0x5, 0xdf, 0x0, 0x0, 0xf0, 0x0, 0xf, 0x0,
    0x0, 0xf0, 0x0, 0xf, 0x0, 0x0, 0xf0, 0x0,
    0xf, 0x0, 0x0, 0xf0, 0x0, 0xe, 0x0, 0x7,
    0xa0, 0xbd, 0xb1, 0x0,

    /* U+004B "K" */
    0x96, 0x0, 0x8, 0xa0, 0x96, 0x0, 0x5c, 0x0,
    0x96, 0x3, 0xd1, 0x0, 0x96, 0x1d, 0x30, 0x0,
    0x96, 0xc6, 0x0, 0x0, 0x9e, 0xca, 0x0, 0x0,
    0x99, 0xd, 0x40, 0x0, 0x96, 0x3, 0xe1, 0x0,
    0x96, 0x0, 0x8a, 0x0, 0x96, 0x0, 0xd, 0x50,
    0x96, 0x0, 0x3, 0xe1,

    /* U+004C "L" */
    0x96, 0x0, 0x0, 0x96, 0x0, 0x0, 0x96, 0x0,
    0x0, 0x96, 0x0, 0x0, 0x96, 0x0, 0x0, 0x96,
    0x0, 0x0, 0x96, 0x0, 0x0, 0x96, 0x0, 0x0,
    0x96, 0x0, 0x0, 0x96, 0x0, 0x0, 0x9e, 0xdd,
    0xd8,

    /* U+004D "M" */
    0xae, 0x0, 0x0, 0x0, 0xe, 0xaa, 0xd5, 0x0,
    0x0, 0x5, 0xda, 0xa8, 0xb0, 0x0, 0x0, 0xb8,
    0xaa, 0x4d, 0x10, 0x0, 0x1d, 0x5a, 0xa4, 0x78,
    0x0, 0x7, 0x75, 0xaa, 0x41, 0xd0, 0x0, 0xd1,
    0x5a, 0xa4, 0xb, 0x40, 0x3a, 0x5, 0xaa, 0x40,
    0x4a, 0x9, 0x40, 0x5a, 0xa4, 0x0, 0xd1, 0xd0,
    0x5, 0xaa, 0x40, 0x8, 0xb8, 0x0, 0x5a, 0xa4,
    0x0, 0x2f, 0x20, 0x5, 0xa0,

    /* U+004E "N" */
    0xac, 0x0, 0x0, 0x5, 0x9a, 0xd7, 0x0, 0x0,
    0x59, 0xa5, 0xd1, 0x0, 0x5, 0x9a, 0x45, 0xb0,
    0x0, 0x59, 0xa4, 0xb, 0x50, 0x5, 0x9a, 0x40,
    0x2d, 0x10, 0x59, 0xa4, 0x0, 0x79, 0x5, 0x9a,
    0x40, 0x0, 0xc3, 0x59, 0xa4, 0x0, 0x3, 0xd5,
    0x9a, 0x40, 0x0, 0x9, 0xc9, 0xa4, 0x0, 0x0,
    0xe, 0x90,

    /* U+004F "O" */
    0x0, 0x18, 0xdd, 0xda, 0x20, 0x0, 0xd, 0x70,
    0x0, 0x4d, 0x40, 0x9, 0x80, 0x0, 0x0, 0x3d,
    0x0, 0xe1, 0x0, 0x0, 0x0, 0xb4, 0x2d, 0x0,
    0x0, 0x0, 0x8, 0x72, 0xc0, 0x0, 0x0, 0x0,
    0x78, 0x1e, 0x0, 0x0, 0x0, 0x8, 0x70, 0xe1,
    0x0, 0x0, 0x0, 0xc3, 0x8, 0x90, 0x0, 0x0,
    0x3d, 0x0, 0xc, 0x80, 0x0, 0x4d, 0x30, 0x0,
    0x8, 0xdd, 0xda, 0x20, 0x0,

    /* U+0050 "P" */
    0x9e, 0xcd, 0xb3, 0x9, 0x60, 0x3, 0xe2, 0x96,
    0x0, 0x8, 0x79, 0x60, 0x0, 0x69, 0x96, 0x0,
    0x9, 0x79, 0x60, 0x4, 0xd1, 0x9d, 0xcc, 0xa2,
    0x9, 0x60, 0x0, 0x0, 0x96, 0x0, 0x0, 0x9,
    0x60, 0x0, 0x0, 0x96, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x8, 0xdd, 0xda, 0x20, 0x0, 0xd, 0x70,
    0x0, 0x4d, 0x40, 0x9, 0x80, 0x0, 0x0, 0x3d,
    0x0, 0xe1, 0x0, 0x0, 0x0, 0xb4, 0x2d, 0x0,
    0x0, 0x0, 0x8, 0x72, 0xc0, 0x0, 0x0, 0x0,
    0x78, 0x1e, 0x0, 0x0, 0x0, 0x8, 0x70, 0xe1,
    0x0, 0x0, 0x0, 0xb4, 0x8, 0x80, 0x0, 0x0,
    0x3d, 0x0, 0xd, 0x70, 0x0, 0x3d, 0x30, 0x0,
    0x19, 0xdc, 0xda, 0x20, 0x0, 0x0, 0x0, 0x5c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7d, 0xc3, 0x0,

    /* U+0052 "R" */
    0xad, 0xcc, 0xc5, 0x0, 0xa5, 0x0, 0x1d, 0x40,
    0xa5, 0x0, 0x4, 0xb0, 0xa5, 0x0, 0x2, 0xd0,
    0xa5, 0x0, 0x5, 0xa0, 0xa5, 0x0, 0x2d, 0x30,
    0xad, 0xce, 0xb3, 0x0, 0xa5, 0x6, 0xb0, 0x0,
    0xa5, 0x0, 0xb6, 0x0, 0xa5, 0x0, 0x1e, 0x20,
    0xa5, 0x0, 0x5, 0xd0,

    /* U+0053 "S" */
    0x2, 0xbc, 0xd9, 0x0, 0xd, 0x30, 0x8, 0x80,
    0x1d, 0x0, 0x1, 0x50, 0xf, 0x10, 0x0, 0x0,
    0x7, 0xd5, 0x0, 0x0, 0x0, 0x4a, 0xe9, 0x0,
    0x0, 0x0, 0x19, 0xb0, 0x0, 0x0, 0x0, 0xf0,
    0x37, 0x0, 0x0, 0xe0, 0x1e, 0x30, 0x6, 0xa0,
    0x3, 0xbd, 0xda, 0x10,

    /* U+0054 "T" */
    0xbd, 0xdf, 0xed, 0xd8, 0x0, 0xa, 0x50, 0x0,
    0x0, 0xa, 0x50, 0x0, 0x0, 0xa, 0x50, 0x0,
    0x0, 0xa, 0x50, 0x0, 0x0, 0xa, 0x50, 0x0,
    0x0, 0xa, 0x50, 0x0, 0x0, 0xa, 0x50, 0x0,
    0x0, 0xa, 0x50, 0x0, 0x0, 0xa, 0x50, 0x0,
    0x0, 0xa, 0x50, 0x0,

    /* U+0055 "U" */
    0xc3, 0x0, 0x0, 0x3c, 0xc3, 0x0, 0x0, 0x3c,
    0xc3, 0x0, 0x0, 0x3c, 0xc3, 0x0, 0x0, 0x3c,
    0xc3, 0x0, 0x0, 0x3c, 0xc3, 0x0, 0x0, 0x3c,
    0xc3, 0x0, 0x0, 0x3c, 0xb4, 0x0, 0x0, 0x4c,
    0x87, 0x0, 0x0, 0x88, 0x1e, 0x30, 0x4, 0xe1,
    0x3, 0xcd, 0xeb, 0x20,

    /* U+0056 "V" */
    0xa6, 0x0, 0x0, 0x3, 0xd0, 0x5b, 0x0, 0x0,
    0x8, 0x80, 0xe, 0x10, 0x0, 0xe, 0x20, 0x9,
    0x60, 0x0, 0x3c, 0x0, 0x4, 0xc0, 0x0, 0x97,
    0x0, 0x0, 0xe1, 0x0, 0xe1, 0x0, 0x0, 0x86,
    0x3, 0xb0, 0x0, 0x0, 0x3c, 0x9, 0x60, 0x0,
    0x0, 0xd, 0x1e, 0x10, 0x0, 0x0, 0x8, 0xaa,
    0x0, 0x0, 0x0, 0x2, 0xf5, 0x0, 0x0,

    /* U+0057 "W" */
    0xa6, 0x0, 0x0, 0xb6, 0x0, 0x0, 0xb5, 0x5a,
    0x0, 0x0, 0xdb, 0x0, 0x0, 0xe1, 0x1e, 0x0,
    0x4, 0x8d, 0x0, 0x3, 0xc0, 0xd, 0x20, 0x9,
    0x4a, 0x40, 0x7, 0x80, 0x9, 0x60, 0xd, 0x5,
    0x80, 0xc, 0x30, 0x5, 0xa0, 0x2b, 0x1, 0xd0,
    0xe, 0x0, 0x1, 0xe0, 0x67, 0x0, 0xc1, 0x4a,
    0x0, 0x0, 0xc2, 0xb2, 0x0, 0x85, 0x86, 0x0,
    0x0, 0x86, 0xd0, 0x0, 0x49, 0xc2, 0x0, 0x0,
    0x4d, 0x90, 0x0, 0xd, 0xd0, 0x0, 0x0, 0xf,
    0x50, 0x0, 0xb, 0x90, 0x0,

    /* U+0058 "X" */
    0x4c, 0x0, 0x0, 0x2d, 0x0, 0xa6, 0x0, 0xc,
    0x40, 0x1, 0xd1, 0x6, 0xa0, 0x0, 0x5, 0xa1,
    0xd1, 0x0, 0x0, 0xb, 0xc6, 0x0, 0x0, 0x0,
    0x7f, 0x20, 0x0, 0x0, 0x1d, 0x6b, 0x0, 0x0,
    0xb, 0x50, 0xb5, 0x0, 0x5, 0xb0, 0x2, 0xe1,
    0x1, 0xd2, 0x0, 0x7, 0x90, 0x97, 0x0, 0x0,
    0xd, 0x40,

    /* U+0059 "Y" */
    0xa7, 0x0, 0x0, 0x3d, 0x1, 0xe0, 0x0, 0xc,
    0x40, 0x8, 0x80, 0x5, 0xb0, 0x0, 0xd, 0x10,
    0xd2, 0x0, 0x0, 0x69, 0x69, 0x0, 0x0, 0x0,
    0xcd, 0x10, 0x0, 0x0, 0x6, 0x90, 0x0, 0x0,
    0x0, 0x69, 0x0, 0x0, 0x0, 0x6, 0x90, 0x0,
    0x0, 0x0, 0x69, 0x0, 0x0, 0x0, 0x6, 0x90,
    0x0, 0x0,

    /* U+005A "Z" */
    0x1d, 0xdd, 0xdd, 0xee, 0x0, 0x0, 0x0, 0xb,
    0x60, 0x0, 0x0, 0x5, 0xb0, 0x0, 0x0, 0x1,
    0xe1, 0x0, 0x0, 0x0, 0xb6, 0x0, 0x0, 0x0,
    0x5b, 0x0, 0x0, 0x0, 0x1e, 0x10, 0x0, 0x0,
    0xb, 0x60, 0x0, 0x0, 0x6, 0xb0, 0x0, 0x0,
    0x1, 0xe1, 0x0, 0x0, 0x0, 0x8f, 0xdd, 0xdd,
    0xdd, 0x20,

    /* U+005B "[" */
    0xac, 0xb3, 0xa4, 0x0, 0xa4, 0x0, 0xa4, 0x0,
    0xa4, 0x0, 0xa4, 0x0, 0xa4, 0x0, 0xa4, 0x0,
    0xa4, 0x0, 0xa4, 0x0, 0xa4, 0x0, 0xa4, 0x0,
    0xac, 0xb3,

    /* U+005C "\\" */
    0xb3, 0x0, 0x0, 0x59, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x7, 0x60, 0x0, 0x1, 0xd0, 0x0, 0x0,
    0xa3, 0x0, 0x0, 0x4a, 0x0, 0x0, 0xd, 0x10,
    0x0, 0x7, 0x70, 0x0, 0x1, 0xd0, 0x0, 0x0,
    0xa4,

    /* U+005D "]" */
    0x8b, 0xe2, 0x0, 0xc2, 0x0, 0xc2, 0x0, 0xc2,
    0x0, 0xc2, 0x0, 0xc2, 0x0, 0xc2, 0x0, 0xc2,
    0x0, 0xc2, 0x0, 0xc2, 0x0, 0xc2, 0x0, 0xc2,
    0x8b, 0xe2,

    /* U+005E "^" */
    0x0, 0x5, 0x40, 0x0, 0x0, 0x1b, 0xb0, 0x0,
    0x0, 0x84, 0x57, 0x0, 0x0, 0xc0, 0xc, 0x0,
    0x7, 0x50, 0x5, 0x70, 0xc, 0x0, 0x0, 0xc0,
    0x76, 0x0, 0x0, 0x67,

    /* U+005F "_" */
    0x19, 0x99, 0x99, 0x93,

    /* U+0060 "`" */
    0x2d, 0x20, 0x2, 0xb0, 0x0, 0x11,

    /* U+0061 "a" */
    0x3, 0xcc, 0xc6, 0x0, 0xa2, 0x0, 0xd2, 0x0,
    0x0, 0x9, 0x50, 0x29, 0xbb, 0xd6, 0x1e, 0x40,
    0x8, 0x64, 0xa0, 0x0, 0xa6, 0x2c, 0x0, 0x3e,
    0x60, 0x7b, 0x98, 0x86,

    /* U+0062 "b" */
    0xc2, 0x0, 0x0, 0xc, 0x20, 0x0, 0x0, 0xc2,
    0x0, 0x0, 0xc, 0x4b, 0xcd, 0x70, 0xcb, 0x10,
    0xc, 0x4c, 0x50, 0x0, 0x4a, 0xc2, 0x0, 0x2,
    0xdc, 0x20, 0x0, 0x2c, 0xc5, 0x0, 0x4, 0xac,
    0xb1, 0x0, 0xc3, 0xd3, 0xbc, 0xd6, 0x0,

    /* U+0063 "c" */
    0x1, 0xbc, 0xc8, 0x0, 0xa5, 0x0, 0xb5, 0x1d,
    0x0, 0x0, 0x3, 0xb0, 0x0, 0x0, 0x3b, 0x0,
    0x0, 0x1, 0xd0, 0x0, 0x11, 0xb, 0x50, 0xb,
    0x40, 0x1b, 0xcc, 0x80,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x86,
    0x0, 0x0, 0x0, 0x86, 0x0, 0xad, 0xc9, 0x86,
    0xa, 0x70, 0x5, 0xe6, 0x1e, 0x0, 0x0, 0xc6,
    0x3b, 0x0, 0x0, 0x96, 0x3b, 0x0, 0x0, 0x96,
    0x1d, 0x0, 0x0, 0xb6, 0xa, 0x50, 0x3, 0xe6,
    0x1, 0xaa, 0xa8, 0x77,

    /* U+0065 "e" */
    0x0, 0xac, 0xc9, 0x0, 0xa, 0x50, 0x7, 0x90,
    0x1d, 0x0, 0x0, 0xe0, 0x3e, 0xbb, 0xbb, 0xd1,
    0x3c, 0x0, 0x0, 0x0, 0x1e, 0x0, 0x0, 0x10,
    0xa, 0x80, 0x5, 0xb0, 0x1, 0xad, 0xca, 0x10,

    /* U+0066 "f" */
    0x0, 0xac, 0x60, 0x4b, 0x0, 0x5, 0x90, 0x7,
    0xde, 0xc2, 0x6, 0x90, 0x0, 0x69, 0x0, 0x6,
    0x90, 0x0, 0x69, 0x0, 0x6, 0x90, 0x0, 0x69,
    0x0, 0x6, 0x90, 0x0,

    /* U+0067 "g" */
    0x1, 0xad, 0xc8, 0x76, 0xa, 0x70, 0x4, 0xe6,
    0x1e, 0x0, 0x0, 0xb6, 0x3c, 0x0, 0x0, 0x86,
    0x3c, 0x0, 0x0, 0x86, 0x1e, 0x0, 0x0, 0xb6,
    0xa, 0x70, 0x5, 0xd6, 0x1, 0xbd, 0xc7, 0x85,
    0x6, 0x10, 0x1, 0xc1, 0x4, 0xcc, 0xcb, 0x20,

    /* U+0068 "h" */
    0xc2, 0x0, 0x0, 0xc, 0x20, 0x0, 0x0, 0xc2,
    0x0, 0x0, 0xc, 0x4b, 0xcc, 0x30, 0xcb, 0x10,
    0x4d, 0xc, 0x50, 0x0, 0xe0, 0xc2, 0x0, 0xc,
    0x1c, 0x20, 0x0, 0xc2, 0xc2, 0x0, 0xc, 0x2c,
    0x20, 0x0, 0xc2, 0xc2, 0x0, 0xc, 0x20,

    /* U+0069 "i" */
    0xc2, 0x10, 0x0, 0xc2, 0xc2, 0xc2, 0xc2, 0xc2,
    0xc2, 0xc2, 0xc2,

    /* U+006A "j" */
    0x0, 0xd2, 0x0, 0x10, 0x0, 0x0, 0x0, 0xd2,
    0x0, 0xd2, 0x0, 0xd2, 0x0, 0xd2, 0x0, 0xd2,
    0x0, 0xd2, 0x0, 0xd2, 0x0, 0xd1, 0x0, 0xe1,
    0x2c, 0xa0,

    /* U+006B "k" */
    0xd1, 0x0, 0x0, 0xd, 0x10, 0x0, 0x0, 0xd1,
    0x0, 0x0, 0xd, 0x10, 0xb, 0x70, 0xd1, 0x9,
    0x80, 0xd, 0x18, 0x80, 0x0, 0xd8, 0xe0, 0x0,
    0xd, 0x99, 0x80, 0x0, 0xd1, 0xd, 0x40, 0xd,
    0x10, 0x2d, 0x10, 0xd1, 0x0, 0x7a, 0x0,

    /* U+006C "l" */
    0xc2, 0xc2, 0xc2, 0xc2, 0xc2, 0xc2, 0xc2, 0xc2,
    0xc2, 0xc2, 0xc2,

    /* U+006D "m" */
    0xd3, 0xaa, 0xb1, 0x69, 0xb8, 0xc, 0x90, 0x7,
    0xc5, 0x0, 0xc3, 0xc4, 0x0, 0x2e, 0x0, 0x8,
    0x6c, 0x20, 0x1, 0xd0, 0x0, 0x77, 0xc2, 0x0,
    0x1d, 0x0, 0x7, 0x7c, 0x20, 0x1, 0xd0, 0x0,
    0x77, 0xc2, 0x0, 0x1d, 0x0, 0x7, 0x7c, 0x20,
    0x1, 0xd0, 0x0, 0x77,

    /* U+006E "n" */
    0xd3, 0xaa, 0xc3, 0xc, 0xa0, 0x2, 0xd0, 0xc4,
    0x0, 0xd, 0xc, 0x20, 0x0, 0xc1, 0xc2, 0x0,
    0xc, 0x2c, 0x20, 0x0, 0xc2, 0xc2, 0x0, 0xc,
    0x2c, 0x20, 0x0, 0xc2,

    /* U+006F "o" */
    0x0, 0x9d, 0xcc, 0x50, 0x9, 0x80, 0x1, 0xd3,
    0x1e, 0x0, 0x0, 0x5a, 0x3c, 0x0, 0x0, 0x2c,
    0x3c, 0x0, 0x0, 0x2c, 0xe, 0x0, 0x0, 0x5a,
    0x9, 0x80, 0x1, 0xd2, 0x0, 0x8d, 0xcc, 0x40,

    /* U+0070 "p" */
    0xd3, 0xaa, 0xb7, 0xc, 0xb0, 0x0, 0xb4, 0xc5,
    0x0, 0x4, 0xac, 0x20, 0x0, 0x1d, 0xc2, 0x0,
    0x2, 0xcc, 0x50, 0x0, 0x5a, 0xcc, 0x10, 0x1c,
    0x3c, 0x4b, 0xcd, 0x50, 0xc2, 0x0, 0x0, 0xc,
    0x20, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0xad, 0xc8, 0x66, 0xa, 0x70, 0x4, 0xe6,
    0x1e, 0x0, 0x0, 0xb6, 0x3b, 0x0, 0x0, 0x96,
    0x3b, 0x0, 0x0, 0x96, 0x1e, 0x0, 0x0, 0xc6,
    0xa, 0x60, 0x5, 0xe6, 0x1, 0xbd, 0xc8, 0x86,
    0x0, 0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x86,

    /* U+0072 "r" */
    0x0, 0x0, 0xc3, 0xb9, 0xc9, 0x0, 0xc5, 0x0,
    0xc3, 0x0, 0xc2, 0x0, 0xc2, 0x0, 0xc2, 0x0,
    0xc2, 0x0,

    /* U+0073 "s" */
    0x9, 0xbb, 0xa0, 0x4a, 0x0, 0x73, 0x4a, 0x0,
    0x0, 0xb, 0xc7, 0x10, 0x0, 0x27, 0xe3, 0x0,
    0x0, 0x59, 0x86, 0x0, 0x77, 0x1b, 0xbb, 0xa0,

    /* U+0074 "t" */
    0x2, 0x30, 0x0, 0x76, 0x0, 0x8e, 0xdc, 0x40,
    0x96, 0x0, 0x9, 0x60, 0x0, 0x96, 0x0, 0x9,
    0x60, 0x0, 0x96, 0x0, 0x8, 0x70, 0x0, 0x2d,
    0xc5,

    /* U+0075 "u" */
    0xe1, 0x0, 0xe, 0x1e, 0x10, 0x0, 0xe1, 0xe1,
    0x0, 0xe, 0x1e, 0x10, 0x0, 0xe1, 0xd1, 0x0,
    0xe, 0x1c, 0x20, 0x0, 0xf1, 0x96, 0x0, 0x7e,
    0x11, 0xba, 0x95, 0xc1,

    /* U+0076 "v" */
    0xc3, 0x0, 0x3, 0xc6, 0x80, 0x0, 0x96, 0x1d,
    0x0, 0xe, 0x0, 0xb3, 0x4, 0xa0, 0x5, 0x90,
    0xa4, 0x0, 0xd, 0xd, 0x0, 0x0, 0x99, 0x80,
    0x0, 0x4, 0xf2, 0x0,

    /* U+0077 "w" */
    0xa3, 0x0, 0x4c, 0x0, 0xb, 0x26, 0x70, 0x9,
    0xc1, 0x0, 0xd0, 0x2c, 0x0, 0xc7, 0x50, 0x49,
    0x0, 0xd0, 0x29, 0x2a, 0x8, 0x40, 0x9, 0x47,
    0x40, 0xc0, 0xc0, 0x0, 0x58, 0xb0, 0x9, 0x4b,
    0x0, 0x1, 0xcb, 0x0, 0x4c, 0x70, 0x0, 0xc,
    0x60, 0x0, 0xf2, 0x0,

    /* U+0078 "x" */
    0x79, 0x0, 0x1d, 0x10, 0xc3, 0xa, 0x50, 0x2,
    0xc4, 0xb0, 0x0, 0x8, 0xe1, 0x0, 0x0, 0xad,
    0x30, 0x0, 0x4a, 0x2d, 0x0, 0xd, 0x10, 0x78,
    0x9, 0x60, 0x0, 0xc3,

    /* U+0079 "y" */
    0xc3, 0x0, 0x3, 0xb5, 0xa0, 0x0, 0xa5, 0xe,
    0x10, 0x1d, 0x0, 0x86, 0x6, 0x80, 0x2, 0xc0,
    0xc1, 0x0, 0xb, 0x6b, 0x0, 0x0, 0x5f, 0x40,
    0x0, 0x2, 0xd0, 0x0, 0x0, 0xc5, 0x0, 0x7,
    0xe7, 0x0, 0x0,

    /* U+007A "z" */
    0x2c, 0xcc, 0xcf, 0x20, 0x0, 0x6, 0xa0, 0x0,
    0x2, 0xd0, 0x0, 0x0, 0xc3, 0x0, 0x0, 0x98,
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x1d, 0x20, 0x0,
    0x7, 0xec, 0xcc, 0xc4,

    /* U+007B "{" */
    0x0, 0xbc, 0x30, 0x59, 0x0, 0x6, 0x70, 0x0,
    0x67, 0x0, 0x6, 0x70, 0x0, 0xa4, 0x0, 0xca,
    0x0, 0x0, 0xb4, 0x0, 0x7, 0x70, 0x0, 0x67,
    0x0, 0x6, 0x70, 0x0, 0x59, 0x0, 0x0, 0xbc,
    0x30,

    /* U+007C "|" */
    0xb2, 0xb2, 0xb2, 0xb2, 0xb2, 0xb2, 0xb2, 0xb2,
    0xb2, 0xb2, 0xb2, 0xb2, 0xb2,

    /* U+007D "}" */
    0xad, 0x30, 0x0, 0x3b, 0x0, 0x1, 0xc0, 0x0,
    0x1c, 0x0, 0x1, 0xc0, 0x0, 0xd, 0x10, 0x0,
    0x4e, 0x30, 0xd, 0x10, 0x1, 0xc0, 0x0, 0x1c,
    0x0, 0x1, 0xc0, 0x0, 0x3b, 0x0, 0xad, 0x40,
    0x0,

    /* U+007E "~" */
    0x3b, 0xc9, 0x30, 0x4, 0x40, 0x3, 0x9b, 0xb3,

    /* U+201C "“" */
    0x90, 0x91, 0x90, 0x90, 0xe2, 0xd2, 0x20, 0x20,

    /* U+201D "”" */
    0xd3, 0xd3, 0x82, 0x72, 0x60, 0x60,

    /* U+2192 "→" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xc1, 0x7, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x7e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x93, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0,

    /* U+4E3B "主" */
    0x0, 0x0, 0x6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8a, 0x0, 0x0, 0x0, 0x3a, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0x30, 0x12, 0x22, 0x22, 0xe2,
    0x22, 0x22, 0x10, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd0, 0x0, 0x0, 0x0,
    0x1, 0xbb, 0xbb, 0xfb, 0xbb, 0xb3, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0x0, 0x0, 0xaa, 0xaa, 0xaa, 0xfa,
    0xaa, 0xaa, 0xa0, 0x21, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x20,

    /* U+4E49 "义" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x26, 0x0, 0x6d, 0x10, 0x1f, 0x30, 0x0, 0x0,
    0xe0, 0x0, 0x8a, 0x7, 0xa0, 0x0, 0x0, 0x9,
    0x60, 0x0, 0x0, 0xe2, 0x0, 0x0, 0x0, 0x2e,
    0x0, 0x0, 0x7a, 0x0, 0x0, 0x0, 0x0, 0x89,
    0x0, 0x1e, 0x10, 0x0, 0x0, 0x0, 0x0, 0xc5,
    0xb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1, 0xda,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc2, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0x90, 0x0, 0x9c,
    0x40, 0x0, 0x3, 0xae, 0x50, 0x0, 0x0, 0x4d,
    0xd7, 0x11, 0xca, 0x10, 0x0, 0x0, 0x0, 0x7,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+4F4D "位" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0x0, 0x96, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xa0, 0x1, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0xd2, 0x0, 0x2, 0xd1, 0x0, 0x0, 0x0, 0x5e,
    0x2c, 0xbb, 0xbb, 0xbb, 0xbc, 0x0, 0xd, 0xd0,
    0x10, 0x0, 0x0, 0x0, 0x10, 0x8, 0xad, 0x0,
    0x40, 0x0, 0xc, 0x50, 0x4, 0xf2, 0xd0, 0xb,
    0x50, 0x0, 0xf1, 0x0, 0x2, 0xd, 0x0, 0x5b,
    0x0, 0x2d, 0x0, 0x0, 0x0, 0xd0, 0x0, 0xf1,
    0x5, 0x90, 0x0, 0x0, 0xd, 0x0, 0xb, 0x60,
    0x95, 0x0, 0x0, 0x0, 0xd0, 0x0, 0x6b, 0xd,
    0x10, 0x0, 0x0, 0xd, 0x0, 0x1, 0x12, 0xc0,
    0x0, 0x0, 0x0, 0xd7, 0xbb, 0xbb, 0xcd, 0xbb,
    0xb7, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+503C "值" */
    0x0, 0x3, 0x40, 0x0, 0xa, 0x0, 0x0, 0x0,
    0x0, 0xa9, 0x0, 0x0, 0xc0, 0x0, 0x0, 0x0,
    0xe, 0x2c, 0xbb, 0xce, 0xbb, 0xbc, 0x0, 0x5,
    0xb0, 0x0, 0x3, 0xa0, 0x0, 0x0, 0x0, 0xdb,
    0x0, 0xbb, 0xce, 0xbb, 0x80, 0x0, 0x9c, 0xb0,
    0xd, 0x0, 0x0, 0x3a, 0x0, 0x2d, 0x3b, 0x0,
    0xeb, 0xbb, 0xbc, 0xa0, 0x0, 0x2, 0xb0, 0xd,
    0x0, 0x0, 0x3a, 0x0, 0x0, 0x2b, 0x0, 0xeb,
    0xbb, 0xbc, 0xa0, 0x0, 0x2, 0xb0, 0xd, 0x0,
    0x0, 0x3a, 0x0, 0x0, 0x2b, 0x0, 0xe9, 0x99,
    0x9a, 0xa0, 0x0, 0x2, 0xb0, 0xd, 0x22, 0x22,
    0x4a, 0x0, 0x0, 0x2b, 0x8a, 0xfa, 0xaa, 0xab,
    0xda, 0x30, 0x3, 0xb2, 0x11, 0x11, 0x11, 0x11,
    0x10,

    /* U+51FA "出" */
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x0, 0x3, 0x0, 0x7, 0x90, 0x0,
    0xe0, 0x0, 0xe1, 0x0, 0x68, 0x0, 0xe, 0x0,
    0xe, 0x0, 0x6, 0x80, 0x0, 0xe0, 0x0, 0xe0,
    0x0, 0x68, 0x0, 0xe, 0x0, 0xe, 0x0, 0x7,
    0xdc, 0xcc, 0xfc, 0xcc, 0xe0, 0x0, 0x0, 0x0,
    0xe, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0xe0,
    0x0, 0x2d, 0x0, 0xd0, 0x0, 0xe, 0x0, 0x1,
    0xc0, 0xd, 0x0, 0x0, 0xe0, 0x0, 0x1c, 0x0,
    0xd0, 0x0, 0xe, 0x0, 0x1, 0xc0, 0xf, 0xbb,
    0xbb, 0xcb, 0xbb, 0xbc, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x2, 0xd0,

    /* U+51FB "击" */
    0x0, 0x0, 0x0, 0x8, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xcb, 0xbb, 0xfb, 0xbb, 0xbd, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe0, 0x0, 0x0, 0x0, 0xb, 0xaa, 0xaa,
    0xaf, 0xaa, 0xaa, 0xaa, 0x30, 0x21, 0x11, 0x11,
    0xe1, 0x11, 0x11, 0x10, 0x0, 0x2c, 0x0, 0xe,
    0x0, 0x3, 0xb0, 0x0, 0x2, 0xb0, 0x0, 0xe0,
    0x0, 0x3a, 0x0, 0x0, 0x2b, 0x0, 0xe, 0x0,
    0x3, 0xa0, 0x0, 0x2, 0xb0, 0x0, 0xe0, 0x0,
    0x3a, 0x0, 0x0, 0x2d, 0x89, 0x9f, 0xab, 0xbc,
    0xa0, 0x0, 0x1, 0x43, 0x22, 0x10, 0x0, 0x4a,
    0x0,

    /* U+52A0 "加" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xa0,
    0x0, 0xa, 0xbb, 0xbb, 0xa, 0xab, 0xea, 0xb4,
    0xd, 0x11, 0x1d, 0x1, 0x4, 0xa0, 0x94, 0xd,
    0x0, 0xd, 0x0, 0x4, 0x90, 0xa3, 0xd, 0x0,
    0xd, 0x0, 0x5, 0x80, 0xa3, 0xd, 0x0, 0xd,
    0x0, 0x7, 0x60, 0xa2, 0xd, 0x0, 0xd, 0x0,
    0x9, 0x40, 0xb1, 0xd, 0x0, 0xd, 0x0, 0xd,
    0x10, 0xc1, 0xd, 0x0, 0xd, 0x0, 0x1d, 0x0,
    0xc1, 0xd, 0x0, 0xd, 0x0, 0x77, 0x0, 0xe0,
    0xd, 0xbb, 0xbd, 0x1, 0xe1, 0x3a, 0xc0, 0xd,
    0x0, 0xd, 0xc, 0x70, 0x16, 0x10, 0x7, 0x0,
    0x4, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5355 "单" */
    0x0, 0x0, 0x60, 0x0, 0x1, 0x80, 0x0, 0x0,
    0x0, 0x8d, 0x20, 0xa, 0x70, 0x0, 0x0, 0x0,
    0x5, 0x50, 0x4b, 0x0, 0x0, 0x0, 0xf, 0xcc,
    0xcf, 0xcc, 0xcf, 0x0, 0x0, 0xd, 0x0, 0xe,
    0x0, 0xd, 0x0, 0x0, 0xf, 0xbb, 0xbf, 0xbb,
    0xbf, 0x0, 0x0, 0xd, 0x0, 0xe, 0x0, 0xd,
    0x0, 0x0, 0xd, 0x0, 0xe, 0x0, 0xd, 0x0,
    0x0, 0xc, 0xcc, 0xcf, 0xcc, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x4c, 0xcc,
    0xcc, 0xcf, 0xcc, 0xcc, 0xcd, 0x0, 0x0, 0x0,
    0xe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x0,

    /* U+53C2 "参" */
    0x0, 0x0, 0x0, 0xb3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xc6, 0x1, 0xd2, 0x0, 0x0, 0x0,
    0x3, 0xc3, 0x0, 0x3, 0xd3, 0x0, 0x0, 0x3,
    0xfc, 0xbb, 0xaa, 0x99, 0xd2, 0x0, 0x0, 0x0,
    0x3, 0xd1, 0x0, 0x1, 0x0, 0x1, 0xdb, 0xbb,
    0xed, 0xbb, 0xbb, 0xbb, 0xc1, 0x1, 0x0, 0xb6,
    0x5, 0xa, 0x60, 0x1, 0x0, 0x0, 0xb7, 0x9,
    0xa1, 0xb, 0x60, 0x0, 0x5, 0xd7, 0x6c, 0x60,
    0xb7, 0xa, 0xc5, 0x1, 0xa2, 0x48, 0x11, 0xb6,
    0x8, 0x45, 0xb1, 0x0, 0x0, 0x27, 0xc3, 0x8,
    0xc1, 0x0, 0x0, 0x0, 0x1c, 0x60, 0x1a, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x9c, 0x50, 0x0,
    0x0, 0x0, 0x9, 0xfd, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+540D "名" */
    0x0, 0x0, 0x7, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x2, 0xed,
    0xcc, 0xcc, 0xc9, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x4d, 0x10, 0x8, 0xe3, 0x81, 0x0, 0x2e, 0x30,
    0x0, 0x42, 0x5, 0xd3, 0x3d, 0x30, 0x0, 0x0,
    0x0, 0x3, 0xed, 0x20, 0x0, 0x0, 0x0, 0x3,
    0xca, 0x10, 0x0, 0x0, 0x0, 0x4b, 0xfc, 0xcc,
    0xcc, 0xcf, 0x6, 0xeb, 0x6c, 0x0, 0x0, 0x0,
    0xe0, 0x1, 0x1, 0xc0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x1c, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x1,
    0xfb, 0xbb, 0xbb, 0xbf, 0x0, 0x0, 0x1c, 0x0,
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+56DE "回" */
    0xc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x0, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xd0, 0xd, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x0, 0xd0, 0xc, 0xcc, 0xcc,
    0x0, 0xd0, 0xd, 0x0, 0xd0, 0x0, 0xc0, 0xd,
    0x0, 0xd0, 0xd, 0x0, 0xc, 0x0, 0xd0, 0xd,
    0x0, 0xd0, 0x0, 0xc0, 0xd, 0x0, 0xd0, 0xe,
    0xaa, 0xae, 0x0, 0xd0, 0xd, 0x0, 0x22, 0x22,
    0x20, 0xd, 0x0, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0xd, 0x11, 0x11, 0x11, 0x11, 0x1d, 0x0,
    0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xf0, 0xe, 0x0,
    0x0, 0x0, 0x0, 0x9, 0x0,

    /* U+56FE "图" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xd, 0x0, 0xd,
    0x20, 0x0, 0x1, 0xc0, 0xd0, 0x7, 0xea, 0xaa,
    0x50, 0x1c, 0xd, 0x6, 0xc3, 0x16, 0xc0, 0x1,
    0xc0, 0xd1, 0xb2, 0xc9, 0xc0, 0x0, 0x1c, 0xd,
    0x0, 0x2b, 0xdc, 0x50, 0x1, 0xc0, 0xd5, 0xbd,
    0x72, 0x2a, 0xeb, 0x2c, 0xd, 0x36, 0x2, 0xba,
    0x21, 0x51, 0xc0, 0xd0, 0x0, 0x60, 0x34, 0x0,
    0x1c, 0xd, 0x0, 0x18, 0xd7, 0x10, 0x1, 0xc0,
    0xd0, 0x0, 0x0, 0x6c, 0x0, 0x1c, 0xf, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xc0, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x2c,

    /* U+5730 "地" */
    0x0, 0x8, 0x0, 0x0, 0xb, 0x40, 0x0, 0x0,
    0x0, 0xd0, 0x0, 0xe0, 0xb2, 0x0, 0x0, 0x0,
    0xd, 0x0, 0xd, 0xb, 0x20, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0xd0, 0xb2, 0x1, 0x10, 0xc, 0xcf,
    0xc8, 0xd, 0x1c, 0x9c, 0xe4, 0x0, 0x0, 0xd0,
    0x57, 0xfc, 0xe4, 0x9, 0x30, 0x0, 0xd, 0x9,
    0x6d, 0xb, 0x20, 0x93, 0x0, 0x0, 0xd0, 0x0,
    0xd0, 0xb2, 0xa, 0x20, 0x0, 0xd, 0x0, 0xd,
    0xb, 0x20, 0xa2, 0x0, 0x0, 0xd0, 0x50, 0xd0,
    0xb3, 0x4d, 0x0, 0x0, 0x4f, 0xd8, 0xd, 0xb,
    0x29, 0x40, 0x1, 0xea, 0x30, 0x0, 0xd0, 0x61,
    0x0, 0x31, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0,
    0xb, 0x50, 0x0, 0x0, 0x0, 0x7d, 0xcc, 0xcc,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5907 "备" */
    0x0, 0x0, 0x7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfc, 0xcc, 0xcc, 0xb1, 0x0, 0x0, 0x4,
    0xd2, 0xa0, 0x0, 0xc5, 0x0, 0x0, 0x1, 0xc3,
    0x6, 0xb3, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8d,
    0x82, 0x9c, 0x61, 0x0, 0x3, 0xbe, 0xc6, 0x0,
    0x0, 0x28, 0xee, 0xb0, 0x5, 0x1d, 0xcc, 0xce,
    0xcc, 0xcb, 0x23, 0x0, 0x0, 0xd0, 0x0, 0xd0,
    0x2, 0xb0, 0x0, 0x0, 0xd, 0xcc, 0xcf, 0xcc,
    0xcb, 0x0, 0x0, 0x0, 0xd0, 0x0, 0xd0, 0x2,
    0xb0, 0x0, 0x0, 0xd, 0xaa, 0xae, 0xaa, 0xab,
    0x0, 0x0, 0x0, 0xd2, 0x22, 0x22, 0x24, 0xb0,
    0x0,

    /* U+5B8C "完" */
    0x0, 0x0, 0x0, 0x29, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0, 0x4,
    0xcc, 0xcc, 0xcc, 0xdc, 0xcc, 0xca, 0x0, 0x49,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb0, 0x4, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x2b, 0x0, 0x0, 0x1c,
    0xcc, 0xcc, 0xcc, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2a, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0x70, 0x0, 0x21, 0x12, 0xe1,
    0x5a, 0x11, 0x11, 0x0, 0x0, 0x0, 0x2c, 0x4,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x5, 0x90, 0x49,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd3, 0x4, 0x90,
    0x0, 0x61, 0x0, 0x5, 0xd8, 0x0, 0x4a, 0x0,
    0xc, 0x30, 0xbf, 0xb4, 0x0, 0x1, 0xcd, 0xdd,
    0x80, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5B9A "定" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0x80, 0x0, 0x0, 0x0, 0x3c,
    0xcc, 0xcc, 0xcd, 0xcc, 0xcc, 0x80, 0x3, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x39, 0x0, 0x3b, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xa0, 0x0, 0x13, 0x22,
    0x22, 0x22, 0x23, 0x12, 0x0, 0x0, 0x89, 0x99,
    0xf9, 0x99, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4c, 0x0, 0xe1,
    0x11, 0x11, 0x0, 0x0, 0x7, 0x70, 0xe, 0xaa,
    0xaa, 0x70, 0x0, 0x0, 0xc5, 0x0, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x4d, 0xd2, 0xe, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0x23, 0xd8, 0xe1, 0x0, 0x0,
    0x10, 0x2e, 0x50, 0x0, 0x6a, 0xcd, 0xef, 0xff,
    0x10, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5B9E "实" */
    0x0, 0x0, 0x7, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0x70, 0x0, 0x0, 0x4c, 0xcc, 0xcc,
    0xcd, 0xcc, 0xcc, 0x55, 0x80, 0x0, 0x0, 0x20,
    0x0, 0x76, 0x58, 0x9, 0x20, 0x2e, 0x0, 0x6,
    0x50, 0x0, 0x3d, 0x82, 0xc0, 0x0, 0x0, 0x1,
    0x80, 0x6, 0x2c, 0x0, 0x0, 0x0, 0x7, 0xd6,
    0x3, 0xa0, 0x0, 0x0, 0x0, 0x1, 0x70, 0x59,
    0x0, 0x0, 0xa, 0xcc, 0xcc, 0xce, 0xdc, 0xcc,
    0xcc, 0x0, 0x0, 0x5, 0xc2, 0x40, 0x0, 0x0,
    0x0, 0x5, 0xd2, 0x2c, 0xa1, 0x0, 0x0, 0x3a,
    0xb1, 0x0, 0x7, 0xe4, 0x3, 0xec, 0x50, 0x0,
    0x0, 0x3, 0xd5, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0,

    /* U+5C0F "小" */
    0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x0, 0x0, 0x0, 0x0, 0x8, 0x30, 0xd,
    0x0, 0x22, 0x0, 0x0, 0x1f, 0x10, 0xd, 0x0,
    0x6b, 0x0, 0x0, 0x4b, 0x0, 0xd, 0x0, 0xd,
    0x30, 0x0, 0x86, 0x0, 0xd, 0x0, 0x6, 0xa0,
    0x0, 0xd1, 0x0, 0xd, 0x0, 0x0, 0xe1, 0x5,
    0xa0, 0x0, 0xd, 0x0, 0x0, 0x97, 0x1e, 0x30,
    0x0, 0xd, 0x0, 0x0, 0x3d, 0x4, 0x0, 0x0,
    0xd, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xec, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0,

    /* U+6001 "态" */
    0x0, 0x0, 0x0, 0x13, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x0, 0xbc,
    0xcc, 0xcf, 0xcf, 0xcc, 0xcc, 0x70, 0x0, 0x0,
    0x6, 0x90, 0x96, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xd1, 0x1, 0xc3, 0x0, 0x0, 0x0, 0x3, 0xd3,
    0x86, 0x2, 0xd7, 0x0, 0x0, 0x4a, 0xd2, 0x0,
    0xb5, 0x1, 0xbe, 0xa0, 0x9, 0x70, 0x0, 0x10,
    0x0, 0x0, 0x35, 0x0, 0x0, 0x0, 0x6, 0xc1,
    0x0, 0x10, 0x0, 0x0, 0xa6, 0x4c, 0x6, 0xc0,
    0xa, 0x60, 0x0, 0x2d, 0x4, 0x90, 0x1, 0x1,
    0xb, 0x70, 0x1d, 0x30, 0x39, 0x0, 0x2, 0xd0,
    0x4, 0x0, 0x30, 0x1, 0xdc, 0xbc, 0xd5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0,

    /* U+6210 "成" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x76, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb3, 0x1e, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xa, 0x30, 0x32, 0x0, 0x0, 0xcc,
    0xcc, 0xcc, 0xed, 0xcc, 0xca, 0x0, 0xc, 0x10,
    0x0, 0x7, 0x60, 0x0, 0x0, 0x0, 0xb1, 0x0,
    0x0, 0x57, 0x2, 0x70, 0x0, 0xb, 0xcc, 0xcd,
    0x3, 0x90, 0x78, 0x0, 0x0, 0xb1, 0x0, 0xd0,
    0x1c, 0xc, 0x20, 0x0, 0xb, 0x10, 0xd, 0x0,
    0xc4, 0xb0, 0x0, 0x0, 0xc0, 0x0, 0xc0, 0x8,
    0xe3, 0x0, 0x0, 0xd, 0x0, 0x2b, 0x0, 0x8d,
    0x0, 0x0, 0x4, 0xa1, 0xfe, 0x50, 0x5c, 0xb6,
    0x3, 0x1, 0xd3, 0x2, 0x1, 0x8c, 0x11, 0xd6,
    0xc4, 0x47, 0x0, 0x0, 0x59, 0x0, 0x1, 0xbc,
    0x0,

    /* U+6570 "数" */
    0x0, 0x0, 0x53, 0x0, 0x1, 0x60, 0x0, 0x0,
    0x8, 0xa, 0x32, 0xb0, 0x5a, 0x0, 0x0, 0x0,
    0xa4, 0xa3, 0xc3, 0x8, 0x50, 0x0, 0x0, 0x35,
    0x4b, 0x66, 0x40, 0xd0, 0x0, 0x0, 0x7, 0x8b,
    0xfc, 0x88, 0x7d, 0xbc, 0xec, 0x20, 0x2, 0xdc,
    0xc9, 0x2e, 0x10, 0x3a, 0x0, 0x4, 0xe3, 0xa3,
    0x78, 0x27, 0x5, 0x80, 0x0, 0xb4, 0xa, 0x10,
    0x0, 0xe0, 0x85, 0x0, 0x2, 0x16, 0xb1, 0x11,
    0xb, 0x3c, 0x10, 0x0, 0xba, 0xfa, 0xae, 0x50,
    0x4c, 0xc0, 0x0, 0x0, 0x59, 0x1, 0xd0, 0x0,
    0xe7, 0x0, 0x0, 0x6, 0xca, 0xb5, 0x0, 0x6c,
    0xd2, 0x0, 0x0, 0x1, 0xcb, 0xd2, 0x7d, 0x3,
    0xe4, 0x0, 0x4b, 0xc3, 0x2, 0xbc, 0x10, 0x3,
    0xe4, 0x0, 0x30, 0x0, 0x4, 0x0, 0x0, 0x0,
    0x0,

    /* U+65B0 "新" */
    0x0, 0x5, 0x50, 0x0, 0x0, 0x0, 0x45, 0x0,
    0x0, 0x1c, 0x40, 0x1, 0x69, 0xc9, 0x60, 0x8,
    0xcc, 0xdc, 0xcb, 0x4c, 0x20, 0x0, 0x0, 0x3,
    0x0, 0x9, 0x13, 0xb0, 0x0, 0x0, 0x0, 0x7a,
    0x4, 0xb0, 0x3b, 0x0, 0x0, 0x0, 0x10, 0xb2,
    0xb2, 0x3, 0xc4, 0x44, 0x43, 0xc, 0xbb, 0xec,
    0xbc, 0x4d, 0x88, 0xe8, 0x60, 0x0, 0x9, 0x40,
    0x3, 0xb0, 0x1c, 0x0, 0x8, 0xcb, 0xec, 0xbc,
    0x4a, 0x1, 0xc0, 0x0, 0x3, 0x19, 0x43, 0x5,
    0x90, 0x1c, 0x0, 0x0, 0xc4, 0x95, 0xd2, 0x87,
    0x1, 0xc0, 0x0, 0x5a, 0x9, 0x45, 0x6d, 0x20,
    0x1c, 0x0, 0xc, 0x10, 0xa4, 0x5, 0xb0, 0x1,
    0xc0, 0x0, 0x0, 0xdd, 0x10, 0xd1, 0x0, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+663E "显" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xbb, 0xbb, 0xbb, 0xbc, 0xb0, 0x0, 0xd0, 0x0,
    0x0, 0x0, 0x2a, 0x0, 0xd, 0x11, 0x11, 0x11,
    0x13, 0xa0, 0x0, 0xfb, 0xbb, 0xbb, 0xbb, 0xba,
    0x0, 0xd, 0x0, 0x0, 0x0, 0x2, 0xa0, 0x0,
    0xfb, 0xbb, 0xbb, 0xbb, 0xcb, 0x0, 0x0, 0x0,
    0xc1, 0xa3, 0x0, 0x20, 0x2, 0xb0, 0xd, 0xb,
    0x10, 0x2f, 0x20, 0xd, 0x50, 0xd0, 0xb1, 0xb,
    0x70, 0x0, 0x4e, 0xd, 0xb, 0x18, 0xc0, 0x0,
    0x0, 0xc2, 0xd0, 0xb1, 0xb2, 0x0, 0x0, 0x0,
    0xd, 0xb, 0x10, 0x0, 0xb, 0xcc, 0xcc, 0xfc,
    0xfc, 0xcc, 0xcd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+6700 "最" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0xbb, 0xbb, 0xbb, 0xf0, 0x0, 0x0,
    0x7, 0xdb, 0xbb, 0xbb, 0xbf, 0x0, 0x0, 0x0,
    0x77, 0x22, 0x22, 0x22, 0xd0, 0x0, 0x0, 0x5,
    0x99, 0x99, 0x99, 0x9a, 0x0, 0x0, 0xa9, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x96, 0x2, 0x2c, 0x11,
    0x3b, 0x11, 0x11, 0x11, 0x10, 0x1, 0xe9, 0x9a,
    0xcb, 0xaa, 0xab, 0x10, 0x0, 0x1c, 0x11, 0x3b,
    0x68, 0x6, 0xc0, 0x0, 0x1, 0xea, 0xab, 0xb0,
    0xd1, 0xd3, 0x0, 0x0, 0x1c, 0x0, 0x2c, 0x26,
    0xe8, 0x0, 0x0, 0x25, 0xe9, 0xbd, 0xe4, 0x4f,
    0x91, 0x0, 0x4, 0xa6, 0x31, 0x2b, 0x8c, 0x17,
    0xe9, 0x20, 0x0, 0x0, 0x3, 0xc7, 0x0, 0x2,
    0x70,

    /* U+672C "本" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0x0, 0x0, 0x0, 0x5, 0xaa,
    0xaa, 0xaf, 0xaa, 0xaa, 0xa7, 0x0, 0x11, 0x11,
    0x1d, 0xfd, 0x11, 0x11, 0x10, 0x0, 0x0, 0x6,
    0xad, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd2,
    0xd1, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xd,
    0x7, 0x70, 0x0, 0x0, 0x0, 0x4d, 0x0, 0xd0,
    0xd, 0x30, 0x0, 0x0, 0x4e, 0x20, 0xd, 0x0,
    0x2e, 0x30, 0x0, 0x6e, 0x5b, 0xbb, 0xfb, 0xbb,
    0x4e, 0x70, 0x2c, 0x20, 0x11, 0x1d, 0x11, 0x10,
    0x3b, 0x20, 0x0, 0x0, 0x0, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0x0,

    /* U+673A "机" */
    0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd1, 0x0, 0x22, 0x22, 0x20, 0x0, 0x0,
    0xd, 0x0, 0xd, 0x99, 0x9f, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0xd0, 0x0, 0xe0, 0x0, 0x5c, 0xcf,
    0xcc, 0x2d, 0x0, 0xe, 0x0, 0x0, 0x4, 0xf0,
    0x0, 0xd0, 0x0, 0xe0, 0x0, 0x0, 0xbf, 0xb0,
    0xc, 0x0, 0xe, 0x0, 0x0, 0x2c, 0xd7, 0xa0,
    0xd0, 0x0, 0xe0, 0x0, 0xc, 0x4d, 0x4, 0xd,
    0x0, 0xd, 0x0, 0x7, 0xb0, 0xd0, 0x2, 0xb0,
    0x0, 0xd0, 0x0, 0x1, 0xd, 0x0, 0x77, 0x0,
    0xd, 0x0, 0x0, 0x0, 0xd0, 0xe, 0x10, 0x0,
    0xd0, 0x51, 0x0, 0xd, 0x1b, 0x80, 0x0, 0xd,
    0xd, 0x30, 0x0, 0xe4, 0xa0, 0x0, 0x0, 0xbe,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+683C "格" */
    0x0, 0x7, 0x10, 0x0, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0xd0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x0, 0x9, 0xdc, 0xcc, 0xc1, 0x0, 0x11,
    0xd1, 0x12, 0xc1, 0x0, 0xa6, 0x0, 0x1b, 0xbf,
    0xb7, 0xc5, 0xd4, 0x5b, 0x0, 0x0, 0x4, 0xf0,
    0x38, 0x2, 0xdd, 0x10, 0x0, 0x0, 0xaf, 0xb0,
    0x0, 0x7d, 0xc8, 0x10, 0x0, 0x2b, 0xd6, 0x55,
    0xc8, 0x0, 0x6d, 0xb3, 0xc, 0x4d, 0x7, 0xae,
    0xcc, 0xcc, 0xc5, 0x6, 0xa0, 0xd0, 0x0, 0xd0,
    0x0, 0xd, 0x0, 0x0, 0xd, 0x0, 0xd, 0x0,
    0x0, 0xd0, 0x0, 0x0, 0xd0, 0x0, 0xd0, 0x0,
    0xd, 0x0, 0x0, 0xd, 0x0, 0xe, 0xbb, 0xbb,
    0xd0, 0x0, 0x0, 0xe0, 0x0, 0xe0, 0x0, 0xe,
    0x0,

    /* U+6D41 "流" */
    0x0, 0x0, 0x0, 0x3, 0xa0, 0x0, 0x0, 0x0,
    0xd, 0x30, 0x0, 0x8, 0xc0, 0x0, 0x0, 0x0,
    0x4e, 0x4a, 0xaa, 0xad, 0xaa, 0xa6, 0x0, 0x0,
    0x31, 0x21, 0x8b, 0x21, 0x11, 0x10, 0x2, 0x0,
    0x0, 0x1d, 0x10, 0xb2, 0x0, 0x1, 0xe7, 0x0,
    0xb, 0x40, 0x4, 0xe2, 0x0, 0x1, 0xc6, 0xb,
    0xea, 0xbb, 0xbc, 0xd0, 0x0, 0x0, 0x0, 0x22,
    0x0, 0x0, 0x7, 0x10, 0x0, 0x3, 0x0, 0xd0,
    0xa5, 0x4b, 0x0, 0x0, 0x0, 0xd2, 0xd, 0xa,
    0x23, 0x90, 0x0, 0x0, 0x4b, 0x1, 0xc0, 0xa2,
    0x39, 0x0, 0x0, 0xc, 0x30, 0x59, 0xa, 0x23,
    0x90, 0x0, 0x7, 0xb0, 0xc, 0x40, 0xa2, 0x39,
    0xa, 0x10, 0xb3, 0xc, 0x90, 0xb, 0x31, 0xdc,
    0xa0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6DFB "添" */
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xd5, 0x4c, 0xcd, 0xdc, 0xc8, 0x0, 0x0,
    0x3, 0x90, 0x0, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0x20, 0x0, 0x10, 0x3, 0x0,
    0x7b, 0xbc, 0xed, 0xeb, 0xbb, 0x0, 0xbb, 0x10,
    0x0, 0xb5, 0xc, 0x30, 0x0, 0x0, 0x58, 0x1,
    0xb8, 0x10, 0x1c, 0x81, 0x0, 0x0, 0x7, 0xe7,
    0xc, 0x40, 0x7, 0xe5, 0x0, 0x3, 0x22, 0x10,
    0xb1, 0x4, 0x40, 0x0, 0x0, 0xb4, 0xc, 0x5b,
    0x4b, 0x2d, 0x0, 0x0, 0x2c, 0x4, 0xb0, 0xb1,
    0xb3, 0x77, 0x0, 0xb, 0x42, 0xe2, 0xb, 0x15,
    0xa0, 0xe1, 0x5, 0xd0, 0x25, 0x0, 0xb1, 0x4,
    0x1, 0x0, 0x85, 0x0, 0x0, 0xcd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+70B9 "点" */
    0x0, 0x0, 0x0, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xac, 0xcc, 0xcc, 0xc0, 0x0, 0x0, 0x0,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xcc, 0xcc, 0xcc,
    0xcd, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0, 0xd,
    0x0, 0x0, 0xd, 0x0, 0x0, 0x0, 0xd, 0x0,
    0x0, 0xd, 0x22, 0x22, 0x22, 0x2d, 0x0, 0x0,
    0xa, 0x99, 0x99, 0x99, 0x99, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x10, 0x3, 0x10, 0x0, 0x8b, 0x1d,
    0x1, 0xe0, 0x6, 0x90, 0x3, 0xe1, 0xc, 0x20,
    0x88, 0x0, 0xd2, 0xd, 0x40, 0x7, 0x40, 0x18,
    0x0, 0x77, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+72B6 "状" */
    0x0, 0x0, 0x81, 0x0, 0x6, 0x20, 0x0, 0x0,
    0x0, 0xd, 0x0, 0x0, 0xe1, 0x40, 0x0, 0x0,
    0x0, 0xd0, 0x0, 0xe, 0x9, 0x80, 0x0, 0xb4,
    0xd, 0x0, 0x0, 0xe0, 0xc, 0x20, 0x3, 0xe1,
    0xd0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x8, 0x6d,
    0x1c, 0xbb, 0xfc, 0xbb, 0xc3, 0x0, 0x0, 0xd0,
    0x0, 0x2f, 0x80, 0x0, 0x0, 0x0, 0x3d, 0x0,
    0x5, 0xac, 0x0, 0x0, 0x0, 0x4c, 0xd0, 0x0,
    0x95, 0xc2, 0x0, 0x0, 0x2d, 0x2d, 0x0, 0x1e,
    0x6, 0xa0, 0x0, 0x2d, 0x40, 0xd0, 0x8, 0x80,
    0xe, 0x40, 0x1, 0x60, 0xd, 0x4, 0xd0, 0x0,
    0x5e, 0x10, 0x0, 0x0, 0xd5, 0xe3, 0x0, 0x0,
    0x9e, 0x20, 0x0, 0xe, 0x74, 0x0, 0x0, 0x0,
    0x40,

    /* U+7387 "率" */
    0x0, 0x0, 0x5, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xd0, 0x0, 0x0, 0x0, 0xb,
    0xbb, 0xbb, 0xec, 0xbb, 0xbb, 0xc2, 0x0, 0x31,
    0x0, 0x1d, 0x33, 0x10, 0x73, 0x0, 0x4, 0xc0,
    0xa, 0x32, 0xe3, 0x5b, 0x0, 0x0, 0x6, 0x73,
    0xa9, 0xf4, 0x1a, 0x0, 0x0, 0x0, 0x5, 0x10,
    0xa5, 0x51, 0x22, 0x0, 0x0, 0x4, 0xc1, 0x95,
    0x7, 0x93, 0xd1, 0x0, 0x4, 0xd1, 0x4d, 0xba,
    0x8d, 0x34, 0xc0, 0x0, 0x32, 0x0, 0x4, 0xa0,
    0x21, 0x1, 0x0, 0x3b, 0xbb, 0xbb, 0xce, 0xbb,
    0xbb, 0xbc, 0x0, 0x0, 0x0, 0x4, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xa0, 0x0, 0x0,
    0x0,

    /* U+7535 "电" */
    0x0, 0x0, 0x7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11,
    0x1d, 0x11, 0x11, 0x10, 0x0, 0xf, 0xaa, 0xaf,
    0xaa, 0xaa, 0xe0, 0x0, 0xd, 0x0, 0xd, 0x0,
    0x0, 0xd0, 0x0, 0xd, 0x0, 0xd, 0x0, 0x0,
    0xd0, 0x0, 0xf, 0xcc, 0xcf, 0xcc, 0xcc, 0xe0,
    0x0, 0xd, 0x0, 0xd, 0x0, 0x0, 0xd0, 0x0,
    0xd, 0x0, 0xd, 0x0, 0x0, 0xd0, 0x0, 0xf,
    0xcc, 0xcf, 0xcc, 0xcc, 0xf0, 0x0, 0x0, 0x0,
    0xd, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0xd,
    0x0, 0x0, 0x0, 0xc0, 0x0, 0x0, 0xc, 0x10,
    0x0, 0x3, 0xd0, 0x0, 0x0, 0x6, 0xdc, 0xcc,
    0xcc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+77E9 "矩" */
    0x0, 0x7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xe0, 0x0, 0x6, 0x55, 0x55, 0x51, 0x0,
    0x59, 0x0, 0x0, 0xe6, 0x66, 0x67, 0x20, 0xc,
    0xce, 0xca, 0xd, 0x0, 0x0, 0x0, 0x5, 0xb0,
    0xd0, 0x0, 0xd0, 0x0, 0x0, 0x0, 0x93, 0xc,
    0x0, 0xf, 0xbb, 0xbc, 0xa0, 0x0, 0x0, 0xc0,
    0x0, 0xd0, 0x0, 0x3a, 0x0, 0xdc, 0xce, 0xcd,
    0x3d, 0x0, 0x3, 0xa0, 0x0, 0x3, 0xa0, 0x0,
    0xd0, 0x0, 0x3a, 0x0, 0x0, 0x6d, 0x10, 0xf,
    0xbb, 0xbb, 0x90, 0x0, 0xb, 0x8d, 0x10, 0xd0,
    0x0, 0x0, 0x0, 0x2, 0xd0, 0x7b, 0xd, 0x0,
    0x0, 0x0, 0x0, 0xc5, 0x0, 0xb2, 0xd0, 0x0,
    0x0, 0x1, 0xba, 0x0, 0x0, 0x1f, 0xcc, 0xcc,
    0xc9, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+786E "确" */
    0x0, 0x0, 0x0, 0x0, 0x75, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf5, 0x44, 0x40, 0xd, 0xcf,
    0xcc, 0x4a, 0x97, 0x7e, 0x70, 0x0, 0x1d, 0x0,
    0x7b, 0x0, 0x4b, 0x0, 0x0, 0x67, 0x8, 0xfd,
    0xbb, 0xec, 0xba, 0x0, 0xd5, 0x48, 0x4b, 0x1,
    0xb0, 0x1b, 0x9, 0xf6, 0x98, 0x2e, 0xaa, 0xea,
    0xab, 0x3b, 0xd0, 0x58, 0x2b, 0x12, 0xb1, 0x2b,
    0x0, 0xd0, 0x58, 0x3b, 0x1, 0xb0, 0x1b, 0x0,
    0xd0, 0x58, 0x4e, 0xbc, 0xeb, 0xcb, 0x0, 0xdb,
    0xc8, 0x87, 0x1, 0xb0, 0x1b, 0x0, 0xe0, 0x58,
    0xd2, 0x1, 0xb0, 0x1b, 0x0, 0x50, 0x7, 0xa0,
    0x1, 0x60, 0x2b, 0x0, 0x0, 0x5e, 0x10, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0,

    /* U+793A "示" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xcc, 0xcc, 0xcc, 0xcc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x0,
    0x0, 0x0, 0xc1, 0x0, 0x0, 0x10, 0x0, 0x20,
    0xc, 0x10, 0x0, 0x0, 0x0, 0x3e, 0x10, 0xc1,
    0xd, 0x40, 0x0, 0xb, 0x50, 0xc, 0x10, 0x2e,
    0x40, 0x7, 0xa0, 0x0, 0xc1, 0x0, 0x3e, 0x47,
    0xd0, 0x0, 0xc, 0x10, 0x0, 0x47, 0x2, 0x0,
    0x11, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+79F0 "称" */
    0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0,
    0x1, 0x49, 0xc0, 0x7, 0xa0, 0x0, 0x0, 0xc,
    0xbe, 0x51, 0x0, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0xb1, 0x0, 0x3f, 0xbb, 0xbb, 0xc1, 0x0, 0xb,
    0x10, 0xb, 0x40, 0x0, 0x4b, 0x3, 0xcb, 0xfc,
    0xc7, 0xb0, 0x28, 0xa, 0x40, 0x0, 0x2f, 0x30,
    0x52, 0x2, 0xb0, 0x0, 0x0, 0x9, 0xfd, 0x30,
    0x0, 0x2b, 0x1, 0x0, 0x1, 0xcb, 0x3d, 0x8,
    0x92, 0xb5, 0xb0, 0x0, 0xb4, 0xb1, 0x10, 0xd1,
    0x2b, 0xa, 0x60, 0x6a, 0xb, 0x10, 0x87, 0x2,
    0xb0, 0xd, 0x20, 0x0, 0xb1, 0x5c, 0x0, 0x2b,
    0x0, 0x44, 0x0, 0xb, 0x10, 0x0, 0x2, 0xa0,
    0x0, 0x0, 0x0, 0xc2, 0x0, 0xd, 0xd4, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7A0B "程" */
    0x0, 0x0, 0x13, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x8b, 0xc9, 0xf, 0xbb, 0xbb, 0xd0, 0x4, 0x3d,
    0x0, 0xd, 0x0, 0x0, 0xd0, 0x0, 0xd, 0x0,
    0xd, 0x0, 0x0, 0xd0, 0x15, 0x5d, 0x55, 0x3e,
    0xbb, 0xbb, 0xc0, 0x16, 0x6f, 0x66, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x91, 0xcb, 0xbb, 0xbb,
    0xb4, 0x0, 0xbe, 0x8b, 0x10, 0xd, 0x0, 0x0,
    0x3, 0xad, 0x8, 0x0, 0xd, 0x0, 0x0, 0xd,
    0x2d, 0x0, 0xab, 0xbf, 0xbb, 0xc0, 0x48, 0xd,
    0x0, 0x0, 0xd, 0x0, 0x0, 0x0, 0xd, 0x0,
    0x0, 0xd, 0x0, 0x0, 0x0, 0xd, 0xa, 0xbb,
    0xbf, 0xbb, 0xbb, 0x0, 0xe, 0x1, 0x0, 0x0,
    0x0, 0x1,

    /* U+7F16 "编" */
    0x0, 0x22, 0x0, 0x2, 0x90, 0x0, 0x0, 0x0,
    0xa7, 0x0, 0x0, 0x9c, 0x0, 0x0, 0x2, 0xc0,
    0x11, 0x99, 0x9d, 0x99, 0x80, 0x9, 0x30, 0xc7,
    0xb1, 0x11, 0x11, 0xd0, 0x3b, 0x25, 0xa1, 0xb0,
    0x0, 0x0, 0xd0, 0x7b, 0xae, 0x11, 0xeb, 0xbb,
    0xbb, 0xd0, 0x0, 0x76, 0x2, 0xb0, 0x0, 0x0,
    0x0, 0x2, 0xa0, 0x3, 0xeb, 0xbb, 0xbb, 0xc3,
    0x2d, 0x78, 0xb5, 0xf0, 0xc0, 0xd0, 0x94, 0x3b,
    0x75, 0x39, 0xf0, 0xc0, 0xd0, 0x94, 0x0, 0x0,
    0x2d, 0xfb, 0xfb, 0xfb, 0xe4, 0x59, 0xcd, 0xba,
    0xd0, 0xc0, 0xd0, 0x94, 0x66, 0x22, 0xe1, 0xd0,
    0xc0, 0xc0, 0x93, 0x0, 0x1, 0x40, 0xe0, 0x0,
    0x3c, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x0,

    /* U+81EA "自" */
    0x0, 0x0, 0x43, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x70, 0x0, 0x0, 0x0, 0x4, 0xd0, 0x0, 0x0,
    0x2, 0xfc, 0xcc, 0xcc, 0xcc, 0xce, 0x1e, 0x0,
    0x0, 0x0, 0x1, 0xe1, 0xe0, 0x0, 0x0, 0x0,
    0x1e, 0x1f, 0xcc, 0xcc, 0xcc, 0xcc, 0xe1, 0xe0,
    0x0, 0x0, 0x0, 0x1e, 0x1e, 0x0, 0x0, 0x0,
    0x1, 0xe1, 0xfb, 0xbb, 0xbb, 0xbb, 0xce, 0x1e,
    0x0, 0x0, 0x0, 0x1, 0xe1, 0xe0, 0x0, 0x0,
    0x0, 0x1e, 0x1f, 0xcc, 0xcc, 0xcc, 0xcc, 0xe2,
    0xe0, 0x0, 0x0, 0x0, 0x1d,

    /* U+83DC "菜" */
    0x0, 0x0, 0x18, 0x0, 0x7, 0x30, 0x0, 0x0,
    0x0, 0x1, 0xd0, 0x0, 0xa3, 0x0, 0x0, 0xc,
    0xcc, 0xcf, 0xcc, 0xce, 0xcc, 0xcc, 0x0, 0x0,
    0x1, 0xc0, 0x0, 0xa3, 0x10, 0x0, 0x0, 0x32,
    0x23, 0x35, 0x69, 0xcf, 0x50, 0x0, 0x7, 0xcb,
    0xa9, 0x85, 0x33, 0x40, 0x0, 0x0, 0x6, 0x90,
    0x2d, 0x0, 0x7b, 0x0, 0x0, 0x0, 0x9, 0x40,
    0xa3, 0x1d, 0x10, 0x0, 0xa, 0xaa, 0xaa, 0xae,
    0xaa, 0xca, 0xaa, 0x10, 0x22, 0x22, 0x5e, 0xde,
    0x72, 0x22, 0x20, 0x0, 0x0, 0x4d, 0x2c, 0x2c,
    0x70, 0x0, 0x0, 0x0, 0x8c, 0x10, 0xc1, 0x9,
    0xd6, 0x10, 0x8, 0xe8, 0x0, 0xc, 0x10, 0x3,
    0xbd, 0x10, 0x32, 0x0, 0x0, 0xd1, 0x0, 0x0,
    0x0,

    /* U+89C6 "视" */
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xc1, 0x0, 0xfb, 0xbb, 0xbf, 0x0, 0x0,
    0x6, 0xd0, 0xd, 0x0, 0x0, 0xd0, 0x0, 0x0,
    0x1, 0x0, 0xd0, 0x32, 0xd, 0x0, 0xb, 0xbb,
    0xdd, 0xd, 0xa, 0x50, 0xd0, 0x0, 0x0, 0xd,
    0x40, 0xd0, 0xa3, 0xd, 0x0, 0x0, 0x9, 0x90,
    0xd, 0xa, 0x20, 0xd0, 0x0, 0x7, 0xfd, 0x10,
    0xd0, 0xd0, 0xd, 0x0, 0x1a, 0xbd, 0x5d, 0x2d,
    0x1e, 0xd0, 0xe0, 0x0, 0x80, 0xd0, 0x41, 0x18,
    0x8d, 0x0, 0x0, 0x0, 0xd, 0x0, 0x2, 0xd1,
    0xd0, 0x0, 0x0, 0x0, 0xd0, 0x1, 0xc5, 0xd,
    0x0, 0x51, 0x0, 0xd, 0x3, 0xd7, 0x0, 0xd0,
    0xc, 0x40, 0x0, 0xe1, 0xb5, 0x0, 0xa, 0xde,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8BA4 "认" */
    0x0, 0x10, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x7, 0xb0, 0x0, 0x0, 0xd0, 0x0, 0x0, 0x0,
    0x7, 0xc0, 0x0, 0xd, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x60, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x10, 0x0, 0x2, 0xcc, 0xc0,
    0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0x6, 0x8d, 0x0, 0x0, 0x0, 0xd, 0x0, 0x0,
    0xa5, 0x95, 0x0, 0x0, 0x0, 0xd0, 0x0, 0x1e,
    0x2, 0xd0, 0x0, 0x0, 0xc, 0x3c, 0x9, 0x80,
    0xa, 0x70, 0x0, 0x2, 0xed, 0x34, 0xd0, 0x0,
    0x1e, 0x40, 0x0, 0x4d, 0x14, 0xe3, 0x0, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x0,
    0x20,

    /* U+8BBE "设" */
    0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xa0, 0x1, 0xfc, 0xcc, 0xe0, 0x0, 0x0,
    0xa, 0x80, 0x1c, 0x0, 0xd, 0x0, 0x0, 0x0,
    0x2, 0x3, 0xa0, 0x0, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x95, 0x0, 0xc, 0x2, 0x1, 0xdc, 0xc0,
    0x6d, 0x0, 0x0, 0x8c, 0xb0, 0x0, 0xd, 0x6,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0x6c,
    0xec, 0xcc, 0xe6, 0x0, 0x0, 0xc, 0x0, 0xb,
    0x10, 0xd, 0x0, 0x0, 0x0, 0xc0, 0x0, 0x4b,
    0x8, 0x70, 0x0, 0x0, 0x1c, 0x39, 0x0, 0x8b,
    0xc0, 0x0, 0x0, 0x2, 0xed, 0x20, 0x7, 0xec,
    0x30, 0x0, 0x0, 0x3c, 0x10, 0x6d, 0x91, 0x2b,
    0xc7, 0x20, 0x0, 0x1, 0xea, 0x20, 0x0, 0x3,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8F6C "转" */
    0x0, 0x4, 0x40, 0x0, 0x5, 0x60, 0x0, 0x0,
    0x0, 0x97, 0x0, 0x0, 0x96, 0x0, 0x0, 0x0,
    0xd, 0x10, 0x1, 0x1b, 0x41, 0x10, 0x1, 0xdc,
    0xec, 0xc6, 0x9a, 0xfa, 0xaa, 0x40, 0x0, 0x77,
    0x20, 0x0, 0x1c, 0x0, 0x0, 0x0, 0xc, 0x2e,
    0x7, 0xbc, 0xeb, 0xbb, 0xc1, 0x4, 0xa0, 0xc0,
    0x0, 0x85, 0x0, 0x0, 0x0, 0xed, 0xbf, 0xba,
    0xd, 0x10, 0x0, 0x0, 0x2, 0x12, 0xd1, 0x23,
    0xeb, 0xbb, 0xe3, 0x0, 0x0, 0xc, 0x28, 0x20,
    0x0, 0x6a, 0x0, 0x1, 0x49, 0xfb, 0x71, 0x60,
    0xd, 0x10, 0x0, 0xda, 0x5c, 0x0, 0x7, 0xdb,
    0x60, 0x0, 0x0, 0x0, 0xc0, 0x0, 0x1, 0xbb,
    0x10, 0x0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0x68,
    0x0,

    /* U+8F91 "辑" */
    0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa7, 0x0, 0x2e, 0xbb, 0xbf, 0x0, 0x1,
    0xd, 0x20, 0x11, 0xb0, 0x0, 0xd0, 0x1, 0xbb,
    0xeb, 0xba, 0x2d, 0x77, 0x7e, 0x0, 0x0, 0x67,
    0x10, 0x0, 0x33, 0x33, 0x40, 0x0, 0xc, 0x2e,
    0xa, 0xce, 0xbb, 0xbf, 0xb7, 0x2, 0xb0, 0xc0,
    0x2, 0xa0, 0x0, 0xd0, 0x0, 0x8d, 0xbf, 0xb8,
    0x2e, 0xbb, 0xbd, 0x0, 0x0, 0x0, 0xc0, 0x2,
    0xa0, 0x0, 0xd0, 0x0, 0x0, 0xc, 0x26, 0x5e,
    0xbb, 0xbd, 0x0, 0x5, 0x8b, 0xfa, 0x73, 0xa0,
    0x0, 0xd0, 0x0, 0xa7, 0x2c, 0x5, 0x8d, 0xab,
    0xcf, 0xfb, 0x0, 0x0, 0xc0, 0x66, 0x54, 0x21,
    0xd0, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8F93 "输" */
    0x0, 0x6, 0x10, 0x0, 0x56, 0x0, 0x0, 0x0,
    0x1, 0xe1, 0x0, 0xd, 0xc0, 0x0, 0x0, 0x14,
    0x7b, 0x34, 0x8, 0x64, 0xa0, 0x0, 0x2, 0x8d,
    0x97, 0x86, 0xa0, 0x5, 0xc2, 0x0, 0x0, 0xc4,
    0x16, 0xcb, 0xbb, 0xb8, 0xdb, 0x0, 0x48, 0xc5,
    0xb1, 0x0, 0x0, 0x3, 0x30, 0xb, 0x1b, 0x10,
    0xab, 0xb4, 0x30, 0xe1, 0x3, 0xfa, 0xeb, 0x7a,
    0x7, 0x4c, 0x2d, 0x0, 0x0, 0xb, 0x10, 0xcb,
    0xd4, 0xc0, 0xd0, 0x0, 0x0, 0xb2, 0x3a, 0x7,
    0x4c, 0xd, 0x0, 0x27, 0xaf, 0xc6, 0xc9, 0xc4,
    0xc0, 0xd0, 0x2, 0x83, 0xb1, 0xb, 0x18, 0x4b,
    0xd, 0x0, 0x0, 0xb, 0x10, 0xa0, 0x74, 0x0,
    0xc0, 0x0, 0x0, 0xb1, 0xc, 0x5d, 0x15, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8FD4 "返" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0,
    0xb, 0x80, 0xa, 0xab, 0xbc, 0xdc, 0x30, 0x0,
    0xb, 0x70, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdc, 0xcc, 0xcc, 0x80, 0x3, 0xba, 0xa0,
    0xc, 0x0, 0x0, 0x95, 0x0, 0x1, 0x1d, 0x0,
    0xc5, 0x90, 0xd, 0x0, 0x0, 0x0, 0xd0, 0xc,
    0x6, 0xc7, 0x90, 0x0, 0x0, 0xd, 0x3, 0x90,
    0x4, 0xf6, 0x0, 0x0, 0x0, 0xd0, 0xa4, 0x2,
    0xc5, 0xc7, 0x0, 0x0, 0xd, 0x3b, 0x28, 0xd3,
    0x0, 0xb8, 0x0, 0x3, 0xe3, 0x1, 0x80, 0x0,
    0x0, 0x20, 0x4, 0xc5, 0xaa, 0x30, 0x0, 0x2,
    0x59, 0x10, 0xa2, 0x0, 0x3a, 0xee, 0xff, 0xed,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8FDC "远" */
    0x3, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4d, 0x30, 0x1d, 0xdd, 0xdd, 0xda, 0x0, 0x0,
    0x2d, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x15, 0x55, 0x55, 0x55, 0x52, 0x2, 0xaa, 0xa2,
    0x87, 0xc9, 0x8d, 0x78, 0x30, 0x3, 0x2d, 0x0,
    0xa, 0x31, 0xb0, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0xc2, 0x1b, 0x0, 0x0, 0x0, 0xd, 0x0, 0xd,
    0x1, 0xb0, 0x0, 0x0, 0x0, 0xd0, 0x6, 0x90,
    0x1b, 0x0, 0x20, 0x0, 0xd, 0x4, 0xd1, 0x1,
    0xd0, 0x3b, 0x0, 0x0, 0xe3, 0xb2, 0x0, 0x9,
    0xcb, 0x30, 0x7, 0xa3, 0x68, 0x20, 0x0, 0x0,
    0x27, 0x30, 0x90, 0x0, 0x29, 0xdd, 0xdd, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0,

    /* U+9009 "选" */
    0x0, 0x0, 0x0, 0x0, 0x25, 0x0, 0x0, 0x0,
    0x16, 0x0, 0x6, 0x14, 0xd0, 0x0, 0x0, 0x0,
    0xb5, 0x2, 0xd0, 0x4d, 0x0, 0x0, 0x0, 0x1,
    0xd1, 0x8d, 0xcd, 0xfc, 0xcc, 0x0, 0x0, 0x2,
    0x2e, 0x10, 0x4d, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x75, 0x58, 0xe5, 0x55, 0x40, 0x4c, 0xce, 0x36,
    0x6c, 0x87, 0xd6, 0x64, 0x0, 0x0, 0xd0, 0x0,
    0xb2, 0x2b, 0x0, 0x0, 0x0, 0xd, 0x0, 0xc,
    0x2, 0xb0, 0x0, 0x0, 0x0, 0xd0, 0x2, 0xa0,
    0x2b, 0x5, 0x60, 0x0, 0xd, 0x1, 0xc2, 0x2,
    0xd3, 0xa7, 0x0, 0x1, 0xe1, 0xc4, 0x0, 0x9,
    0xb8, 0x0, 0x19, 0xa5, 0xa8, 0x20, 0x0, 0x0,
    0x2, 0x10, 0x70, 0x0, 0x4b, 0xdd, 0xde, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+9645 "际" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc,
    0xce, 0x97, 0xbb, 0xbb, 0xbb, 0x0, 0xb1, 0xe,
    0x21, 0x11, 0x11, 0x11, 0x0, 0xb1, 0x4b, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb1, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb2, 0xe0, 0x3c, 0xcc, 0xcc,
    0xcc, 0xc0, 0xb1, 0x68, 0x0, 0x0, 0xd0, 0x0,
    0x0, 0xb1, 0xd, 0x0, 0x0, 0xd0, 0x0, 0x0,
    0xb1, 0xa, 0x22, 0xa0, 0xd0, 0x90, 0x0, 0xb2,
    0x2a, 0x18, 0x50, 0xd0, 0x99, 0x0, 0xb3, 0xe6,
    0x2b, 0x0, 0xd0, 0xc, 0x70, 0xb1, 0x0, 0xc3,
    0x0, 0xd0, 0x1, 0xb0, 0xb1, 0x0, 0x10, 0x13,
    0xc0, 0x0, 0x0, 0xc2, 0x0, 0x0, 0xbe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+9875 "页" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbc,
    0xcc, 0xcc, 0xfc, 0xcc, 0xcc, 0xc0, 0x0, 0x0,
    0x1, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x8d, 0xcc, 0xcc,
    0xcc, 0xf1, 0x0, 0x0, 0x76, 0x0, 0x0, 0x0,
    0xc1, 0x0, 0x0, 0x76, 0x0, 0xb5, 0x0, 0xc1,
    0x0, 0x0, 0x76, 0x0, 0xc2, 0x0, 0xc1, 0x0,
    0x0, 0x76, 0x0, 0xd2, 0x0, 0xc1, 0x0, 0x0,
    0x76, 0x0, 0xf0, 0x0, 0xc1, 0x0, 0x0, 0x76,
    0x5, 0xb2, 0x30, 0xd2, 0x0, 0x0, 0x43, 0x3d,
    0x23, 0xdb, 0x30, 0x0, 0x0, 0x39, 0xd3, 0x0,
    0x5, 0xea, 0x10, 0x3f, 0xd7, 0x0, 0x0, 0x0,
    0x9, 0x80, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+9879 "项" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xcc, 0xdf, 0xcc, 0xc6, 0xd,
    0xce, 0xcb, 0x0, 0x7, 0x90, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x0, 0xd,
    0x0, 0x4e, 0xcc, 0xcc, 0xd7, 0x0, 0x0, 0xd0,
    0x4, 0x90, 0x0, 0x7, 0x60, 0x0, 0xd, 0x0,
    0x49, 0x6, 0xc0, 0x76, 0x0, 0x0, 0xd0, 0x4,
    0x90, 0x59, 0x7, 0x60, 0x0, 0xd, 0x15, 0x49,
    0x6, 0x80, 0x76, 0x0, 0x37, 0xfc, 0x64, 0x90,
    0x86, 0x7, 0x60, 0xb, 0x82, 0x0, 0x49, 0xd,
    0x30, 0x77, 0x0, 0x0, 0x0, 0x1, 0x37, 0xc8,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x29, 0xd1, 0xa,
    0xd2, 0x0, 0x0, 0x3, 0xcf, 0x80, 0x0, 0x7,
    0xe2, 0x0, 0x0, 0x5, 0x10, 0x0, 0x0, 0x4,
    0x0,

    /* U+9891 "频" */
    0x0, 0x0, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xd, 0x0, 0xbc, 0xcf, 0xcc, 0xc2, 0x0,
    0xd0, 0xdb, 0xa0, 0x4, 0x90, 0x0, 0x0, 0xd,
    0xd, 0x1, 0x28, 0xcb, 0x89, 0x30, 0x0, 0xd0,
    0xd0, 0x2, 0xc5, 0x55, 0xa5, 0x2, 0xcc, 0xcc,
    0xcc, 0x7a, 0x5, 0x18, 0x50, 0x0, 0x0, 0xc1,
    0x2, 0xa0, 0xe2, 0x85, 0x0, 0x9, 0x2d, 0x8,
    0x6a, 0xd, 0x8, 0x50, 0x2, 0xc0, 0xd1, 0xe3,
    0xa0, 0xd0, 0x85, 0x0, 0xa5, 0xd, 0x68, 0x2a,
    0x1d, 0x8, 0x50, 0x19, 0x0, 0x6d, 0x11, 0x66,
    0x80, 0x21, 0x0, 0x0, 0x2c, 0x40, 0x3, 0xd3,
    0xd4, 0x0, 0x1, 0x7d, 0x30, 0x18, 0xd3, 0x2,
    0xd6, 0x1, 0xd8, 0x10, 0x1d, 0x81, 0x0, 0x1,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+98CE "风" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfb, 0xbb, 0xbb, 0xbb, 0xf0, 0x0, 0x0,
    0x1d, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x1,
    0xe0, 0x0, 0x3, 0xa0, 0xe0, 0x0, 0x0, 0xe,
    0x78, 0x0, 0x95, 0xe, 0x0, 0x0, 0x1, 0xe0,
    0xa8, 0x1d, 0x0, 0xd0, 0x0, 0x0, 0x1d, 0x0,
    0xad, 0x60, 0xd, 0x0, 0x0, 0x3, 0xc0, 0x3,
    0xf6, 0x0, 0xe0, 0x0, 0x0, 0x5b, 0x0, 0xc3,
    0xd4, 0xe, 0x0, 0x0, 0x8, 0x80, 0xb5, 0x2,
    0xe2, 0xd0, 0x0, 0x0, 0xc5, 0xc7, 0x0, 0x4,
    0x9b, 0x30, 0x0, 0x3e, 0x47, 0x0, 0x0, 0x0,
    0x77, 0x33, 0xc, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xe9, 0x73, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 66, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 68, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 11, .adv_w = 87, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 19, .adv_w = 143, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 69, .adv_w = 126, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 117, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 161, .adv_w = 180, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 222, .adv_w = 53, .box_w = 2, .box_h = 3, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 225, .adv_w = 70, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 258, .adv_w = 70, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 284, .adv_w = 99, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 299, .adv_w = 160, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 331, .adv_w = 53, .box_w = 2, .box_h = 4, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 335, .adv_w = 120, .box_w = 5, .box_h = 1, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 338, .adv_w = 53, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 340, .adv_w = 91, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 373, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 417, .adv_w = 126, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 450, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 494, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 538, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 582, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 626, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 670, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 714, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 758, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 802, .adv_w = 53, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 810, .adv_w = 53, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 820, .adv_w = 160, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 852, .adv_w = 160, .box_w = 8, .box_h = 4, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 868, .adv_w = 160, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 900, .adv_w = 105, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 939, .adv_w = 228, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1030, .adv_w = 153, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1085, .adv_w = 134, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1124, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1174, .adv_w = 165, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1224, .adv_w = 121, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1263, .adv_w = 114, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1296, .adv_w = 162, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1351, .adv_w = 166, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1401, .adv_w = 59, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1412, .adv_w = 82, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1440, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1484, .adv_w = 111, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1517, .adv_w = 208, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1578, .adv_w = 175, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1628, .adv_w = 182, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1689, .adv_w = 132, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1728, .adv_w = 182, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1800, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1844, .adv_w = 123, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1888, .adv_w = 124, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1932, .adv_w = 160, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1976, .adv_w = 147, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2031, .adv_w = 219, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2108, .adv_w = 138, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2158, .adv_w = 131, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2208, .adv_w = 137, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2258, .adv_w = 70, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2284, .adv_w = 89, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2317, .adv_w = 70, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2343, .adv_w = 160, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2371, .adv_w = 100, .box_w = 8, .box_h = 1, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2375, .adv_w = 62, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 2381, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2409, .adv_w = 138, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2448, .adv_w = 109, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2476, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2520, .adv_w = 123, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2552, .adv_w = 71, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2580, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2620, .adv_w = 132, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2659, .adv_w = 54, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2670, .adv_w = 54, .box_w = 4, .box_h = 13, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2696, .adv_w = 113, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2735, .adv_w = 54, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2746, .adv_w = 202, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2790, .adv_w = 132, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2818, .adv_w = 138, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2850, .adv_w = 138, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2885, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2925, .adv_w = 81, .box_w = 4, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2943, .adv_w = 98, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2967, .adv_w = 76, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2992, .adv_w = 132, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3020, .adv_w = 112, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3048, .adv_w = 168, .box_w = 11, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3092, .adv_w = 106, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3120, .adv_w = 112, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3155, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3183, .adv_w = 70, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3216, .adv_w = 56, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3229, .adv_w = 70, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3262, .adv_w = 160, .box_w = 8, .box_h = 2, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 3270, .adv_w = 85, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 3278, .adv_w = 85, .box_w = 4, .box_h = 3, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 3284, .adv_w = 240, .box_w = 15, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 3337, .adv_w = 240, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3435, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3548, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3661, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3766, .adv_w = 240, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3857, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3962, .adv_w = 240, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4074, .adv_w = 240, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4172, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4285, .adv_w = 240, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4383, .adv_w = 240, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4468, .adv_w = 240, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4559, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4672, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4777, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4890, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5010, .adv_w = 240, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 5108, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5213, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5326, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5439, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5552, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5665, .adv_w = 240, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 5763, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5868, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5981, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6094, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6199, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6312, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6425, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6530, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6635, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6740, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6845, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6958, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7063, .adv_w = 240, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7161, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7274, .adv_w = 240, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7372, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7477, .adv_w = 240, .box_w = 11, .box_h = 14, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 7554, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7659, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7772, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7877, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7990, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8095, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8208, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8321, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8434, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8547, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8660, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8765, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8870, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8983, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9096, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1, 0x176, 0x2e1f, 0x2e2d, 0x2f31, 0x3020, 0x31de,
    0x31df, 0x3284, 0x3339, 0x33a6, 0x33f1, 0x36c2, 0x36e2, 0x3714,
    0x38eb, 0x3b70, 0x3b7e, 0x3b82, 0x3bf3, 0x3fe5, 0x41f4, 0x4554,
    0x4594, 0x4622, 0x46e4, 0x4710, 0x471e, 0x4820, 0x4d25, 0x4ddf,
    0x509d, 0x529a, 0x536b, 0x5519, 0x57cd, 0x5852, 0x591e, 0x59d4,
    0x59ef, 0x5efa, 0x61ce, 0x63c0, 0x69aa, 0x6b88, 0x6ba2, 0x6f50,
    0x6f75, 0x6f77, 0x6fb8, 0x6fc0, 0x6fed, 0x7629, 0x7859, 0x785d,
    0x7875, 0x78b2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 8220, .range_length = 30899, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 58, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_FontDengXianMedium = {
#else
lv_font_t ui_font_FontDengXianMedium = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_FONTDENGXIANMEDIUM*/

