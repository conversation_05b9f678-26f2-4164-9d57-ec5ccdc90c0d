<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>10</zoom_level>
  <element>
    <id>UMLObject</id>
    <coordinates>
      <x>520</x>
      <y>210</y>
      <w>450</w>
      <h>530</h>
    </coordinates>
    <panel_attributes>Delay_vDelayUs Flow
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>710</x>
      <y>250</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>710</x>
      <y>260</y>
      <w>30</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>700</x>
      <y>310</y>
      <w>40</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>810</x>
      <y>400</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>&lt;= DELAY_MAX_US</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>730</x>
      <y>310</y>
      <w>180</w>
      <h>110</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[us &gt; MAX_US]</panel_attributes>
    <additional_attributes>150.0;90.0;150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>640</x>
      <y>390</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>Calculate Cycles</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>710</x>
      <y>340</y>
      <w>30</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>640</x>
      <y>460</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>Get Start Cycles</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>710</x>
      <y>420</y>
      <w>30</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>640</x>
      <y>530</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>Wait Loop</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>710</x>
      <y>490</y>
      <w>30</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>700</x>
      <y>600</y>
      <w>40</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>710</x>
      <y>560</y>
      <w>30</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>570</x>
      <y>540</y>
      <w>150</w>
      <h>110</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[elapsed &lt; cycles]
</panel_attributes>
    <additional_attributes>70.0;10.0;10.0;10.0;10.0;80.0;130.0;80.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>710</x>
      <y>690</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>710</x>
      <y>630</y>
      <w>150</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[elapsed &gt;= cycles]</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>730</x>
      <y>430</y>
      <w>170</w>
      <h>210</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;190.0;150.0;190.0;150.0;10.0</additional_attributes>
  </element>
</diagram>
