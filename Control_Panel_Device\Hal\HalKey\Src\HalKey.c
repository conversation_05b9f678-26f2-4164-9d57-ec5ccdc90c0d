//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file HalKey.c
*
* @brief Key hardware abstraction layer implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "<PERSON><PERSON>ey.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define HALKEY_DEFAULT_SCAN_DELAY_US_D            10U                                               /* Default key scan delay time (microseconds) */
#define HALKEY_DEFAULT_INPUT_LINE_1_PORT_D        (KEY_INPUT_LINE_1_GPIO_Port)                      /* Default input line 1 GPIO port */
#define HALKEY_DEFAULT_INPUT_LINE_1_PIN_D         (KEY_INPUT_LINE_1_Pin)                            /* Default input line 1 GPIO pin */
#define HALKEY_DEFAULT_INPUT_LINE_2_PORT_D        (KEY_INPUT_LINE_2_GPIO_Port)                      /* Default input line 2 GPIO port */
#define HALKEY_DEFAULT_INPUT_LINE_2_PIN_D         (KEY_INPUT_LINE_2_Pin)                            /* Default input line 2 GPIO pin */
#define HALKEY_DEFAULT_INPUT_LINE_3_PORT_D        (KEY_INPUT_LINE_3_GPIO_Port)                      /* Default input line 3 GPIO port */
#define HALKEY_DEFAULT_INPUT_LINE_3_PIN_D         (KEY_INPUT_LINE_3_Pin)                            /* Default input line 3 GPIO pin */
#define HALKEY_DEFAULT_INPUT_LINE_4_PORT_D        (KEY_INPUT_LINE_4_GPIO_Port)                      /* Default input line 4 GPIO port */
#define HALKEY_DEFAULT_INPUT_LINE_4_PIN_D         (KEY_INPUT_LINE_4_Pin)                            /* Default input line 4 GPIO pin */
#define HALKEY_DEFAULT_OUTPUT_LINE_1_PORT_D       (KEY_OUTPUT_LINE_1_GPIO_Port)                     /* Default output line 1 GPIO port */
#define HALKEY_DEFAULT_OUTPUT_LINE_1_PIN_D        (KEY_OUTPUT_LINE_1_Pin)                           /* Default output line 1 GPIO pin */
#define HALKEY_DEFAULT_OUTPUT_LINE_2_PORT_D       (KEY_OUTPUT_LINE_2_GPIO_Port)                     /* Default output line 2 GPIO port */
#define HALKEY_DEFAULT_OUTPUT_LINE_2_PIN_D        (KEY_OUTPUT_LINE_2_Pin)                           /* Default output line 2 GPIO pin */
#define HALKEY_DEFAULT_OUTPUT_LINE_3_PORT_D       (KEY_OUTPUT_LINE_3_GPIO_Port)                     /* Default output line 3 GPIO port */
#define HALKEY_DEFAULT_OUTPUT_LINE_3_PIN_D        (KEY_OUTPUT_LINE_3_Pin)                           /* Default output line 3 GPIO pin */
#define HALKEY_DEFAULT_OUTPUT_LINE_4_PORT_D       (KEY_OUTPUT_LINE_4_GPIO_Port)                     /* Default output line 4 GPIO port */
#define HALKEY_DEFAULT_OUTPUT_LINE_4_PIN_D        (KEY_OUTPUT_LINE_4_Pin)                           /* Default output line 4 GPIO pin */

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize key hardware abstraction layer.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param psConfig [in]: Configuration parameter pointer.
* @return Initialization status.
*/
HALKEY_STATUS_E HalKey_eInit(HALKEY_HANDLE_T* const psHandle, const HALKEY_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (psConfig == NULL_D))
    {
        return HALKEY_ERROR_E;
    }
    
    /* Check validity of configuration parameters */
    for (U8 i = 0U; i < HALKEY_MATRIX_ROWS_D; i++)
    {
        if ((psConfig->asInputPins[i].psGpioPort == NULL_D) || (psConfig->asInputPins[i].u16GpioPin == 0U))
        {
            return HALKEY_ERROR_E;
        }
    }

    for (U8 i = 0U; i < HALKEY_MATRIX_COLS_D; i++)
    {
        if ((psConfig->asOutputPins[i].psGpioPort == NULL_D) || (psConfig->asOutputPins[i].u16GpioPin == 0U))
        {
            return HALKEY_ERROR_E;
        }
    }
    
    if (psConfig->u32ScanDelayUs == 0U)
    {
        return HALKEY_ERROR_E;
    }
    
    /* Save configuration parameters */
    psHandle->sConfig = *psConfig;
    
    /* Initialize other members of handle */
    psHandle->bInitialized = TRUE_D;
    psHandle->u16LastKeyMatrix = 0U;
    
    /* Configure all output pins as push-pull output, initial state is high level */
    for (U8 i = 0U; i < HALKEY_MATRIX_COLS_D; i++)
    {
        if (HalKey_eSetOutputLine(psHandle, i, TRUE_D) != HALKEY_OK_E)
        {
            return HALKEY_ERROR_E;
        }
    }
    
    return HALKEY_OK_E;
}

/**
* @brief Deinitialize key hardware abstraction layer.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Deinitialization status.
*/
HALKEY_STATUS_E HalKey_eDeInit(HALKEY_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if (psHandle == NULL_D)
    {
        return HALKEY_ERROR_E;
    }

    if (psHandle->bInitialized)
    {
        /* Set all output lines to high level (invalid state) */
        for (U8 i = 0U; i < HALKEY_MATRIX_COLS_D; i++)
        {
            if (HalKey_eSetOutputLine(psHandle, i, TRUE_D) != HALKEY_OK_E)
            {
                return HALKEY_ERROR_E;
            }
        }
        
        /* Clear handle */
        psHandle->u16LastKeyMatrix = 0U;
        psHandle->bInitialized = FALSE_D;
    }

    return HALKEY_OK_E;
}

/**
* @brief Get default configuration.
* @remark None.
*
* @param psConfig [out]: Configuration structure pointer for returning default configuration.
* @return Operation status.
*/
HALKEY_STATUS_E HalKey_eGetDefaultConfig(HALKEY_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return HALKEY_ERROR_E;
    }
    
    /* Configure input pins (row lines) - use default configuration macro definitions */
    psConfig->asInputPins[0U].psGpioPort = HALKEY_DEFAULT_INPUT_LINE_1_PORT_D;   /* Input line 1 GPIO port */
    psConfig->asInputPins[0U].u16GpioPin = HALKEY_DEFAULT_INPUT_LINE_1_PIN_D;    /* Input line 1 GPIO pin */
    psConfig->asInputPins[1U].psGpioPort = HALKEY_DEFAULT_INPUT_LINE_2_PORT_D;   /* Input line 2 GPIO port */
    psConfig->asInputPins[1U].u16GpioPin = HALKEY_DEFAULT_INPUT_LINE_2_PIN_D;    /* Input line 2 GPIO pin */
    psConfig->asInputPins[2U].psGpioPort = HALKEY_DEFAULT_INPUT_LINE_3_PORT_D;   /* Input line 3 GPIO port */
    psConfig->asInputPins[2U].u16GpioPin = HALKEY_DEFAULT_INPUT_LINE_3_PIN_D;    /* Input line 3 GPIO pin */
    psConfig->asInputPins[3U].psGpioPort = HALKEY_DEFAULT_INPUT_LINE_4_PORT_D;   /* Input line 4 GPIO port */
    psConfig->asInputPins[3U].u16GpioPin = HALKEY_DEFAULT_INPUT_LINE_4_PIN_D;    /* Input line 4 GPIO pin */
    
    /* Configure output pins (column lines) - use default configuration macro definitions */
    psConfig->asOutputPins[0U].psGpioPort = HALKEY_DEFAULT_OUTPUT_LINE_1_PORT_D; /* Output line 1 GPIO port */
    psConfig->asOutputPins[0U].u16GpioPin = HALKEY_DEFAULT_OUTPUT_LINE_1_PIN_D;  /* Output line 1 GPIO pin */
    psConfig->asOutputPins[1U].psGpioPort = HALKEY_DEFAULT_OUTPUT_LINE_2_PORT_D; /* Output line 2 GPIO port */
    psConfig->asOutputPins[1U].u16GpioPin = HALKEY_DEFAULT_OUTPUT_LINE_2_PIN_D;  /* Output line 2 GPIO pin */
    psConfig->asOutputPins[2U].psGpioPort = HALKEY_DEFAULT_OUTPUT_LINE_3_PORT_D; /* Output line 3 GPIO port */
    psConfig->asOutputPins[2U].u16GpioPin = HALKEY_DEFAULT_OUTPUT_LINE_3_PIN_D;  /* Output line 3 GPIO pin */
    psConfig->asOutputPins[3U].psGpioPort = HALKEY_DEFAULT_OUTPUT_LINE_4_PORT_D; /* Output line 4 GPIO port */
    psConfig->asOutputPins[3U].u16GpioPin = HALKEY_DEFAULT_OUTPUT_LINE_4_PIN_D;  /* Output line 4 GPIO pin */
    
    /* Set default scan delay */
    psConfig->u32ScanDelayUs = HALKEY_DEFAULT_SCAN_DELAY_US_D;
    
    return HALKEY_OK_E;
}

/**
* @brief Scan key matrix.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param pu16KeyMatrix [out]: Key matrix status pointer (16-bit bitmap, each bit represents one key).
* @return Scan status.
*/
HALKEY_STATUS_E HalKey_eScanMatrix(HALKEY_HANDLE_T* const psHandle, U16* const pu16KeyMatrix)
{
    /* Parameter and initialization status check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (pu16KeyMatrix == NULL_D))
    {
        return HALKEY_ERROR_E;
    }
    
    /* Clear key matrix status */
    *pu16KeyMatrix = 0U;
    
    /* Scan key matrix column by column */
    for (U8 u8Col = 0U; u8Col < HALKEY_MATRIX_COLS_D; u8Col++)
    {
        /* Set current column to low level, other columns to high level */
        for (U8 i = 0U; i < HALKEY_MATRIX_COLS_D; i++)
        {
            if (HalKey_eSetOutputLine(psHandle, i, (i != u8Col)) != HALKEY_OK_E)
            {
                return HALKEY_ERROR_E;
            }
        }
        
        /* Delay to wait for GPIO level stabilization */
        Utility_vDelayUs(psHandle->sConfig.u32ScanDelayUs);
        
        /* Read input status of all rows */
        for (U8 u8Row = 0U; u8Row < HALKEY_MATRIX_ROWS_D; u8Row++)
        {
            BOOL bKeyPressed = FALSE_D;

            if (HalKey_eReadInputLine(psHandle, u8Row, &bKeyPressed) != HALKEY_OK_E)
            {
                return HALKEY_ERROR_E;
            }

            /* When key is pressed, input line is at low level */
            if (!bKeyPressed)
            {
                /* Calculate key index in matrix */
                U8 u8KeyIndex = u8Row * HALKEY_MATRIX_COLS_D + u8Col;
                /* Set corresponding bit */
                *pu16KeyMatrix |= (1U << u8KeyIndex);
            }
        }
    }
    
    /* Restore all output lines to high level (invalid state) */
    for (U8 i = 0U; i < HALKEY_MATRIX_COLS_D; i++)
    {
        if (HalKey_eSetOutputLine(psHandle, i, TRUE_D) != HALKEY_OK_E)
        {
            return HALKEY_ERROR_E;
        }
    }
    
    /* Save current scan result */
    psHandle->u16LastKeyMatrix = *pu16KeyMatrix;
    
    return HALKEY_OK_E;
}

/**
* @brief Set output line status.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param u8OutputLine [in]: Output line number (0-3, corresponding to matrix columns).
* @param bState [in]: Output status (TRUE_D-high level, FALSE_D-low level).
* @return Operation status.
*/
HALKEY_STATUS_E HalKey_eSetOutputLine(const HALKEY_HANDLE_T* const psHandle, const U8 u8OutputLine, const BOOL bState)
{
    /* Parameter and initialization status check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (u8OutputLine >= HALKEY_MATRIX_COLS_D))
    {
        return HALKEY_ERROR_E;
    }
    
    /* Convert BOOL value to GPIO status */
    GPIO_PinState pinState = bState ? GPIO_PIN_SET : GPIO_PIN_RESET;
    
    /* Set GPIO output status */
    HAL_GPIO_WritePin(psHandle->sConfig.asOutputPins[u8OutputLine].psGpioPort,
                      psHandle->sConfig.asOutputPins[u8OutputLine].u16GpioPin,
                      pinState);
    
    return HALKEY_OK_E;
}

/**
* @brief Read input line status.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param u8InputLine [in]: Input line number (0-3, corresponding to matrix rows).
* @param pbState [out]: Input status pointer (TRUE_D-high level, FALSE_D-low level).
* @return Operation status.
*/
HALKEY_STATUS_E HalKey_eReadInputLine(const HALKEY_HANDLE_T* const psHandle, const U8 u8InputLine, BOOL* const pbState)
{
    /* Parameter and initialization status check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (u8InputLine >= HALKEY_MATRIX_ROWS_D) || (pbState == NULL_D))
    {
        return HALKEY_ERROR_E;
    }
    
    /* Read GPIO input status */
    GPIO_PinState pinState = HAL_GPIO_ReadPin(psHandle->sConfig.asInputPins[u8InputLine].psGpioPort,
                                              psHandle->sConfig.asInputPins[u8InputLine].u16GpioPin);
    
    /* Convert GPIO status to BOOL value */
    *pbState = (pinState == GPIO_PIN_SET);
    
    return HALKEY_OK_E;
}

/**
* @brief Get last scanned key matrix status.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param pu16KeyMatrix [out]: Key matrix status pointer (16-bit bitmap, each bit represents one key).
* @return Operation status.
*/
HALKEY_STATUS_E HalKey_eGetLastKeyMatrix(const HALKEY_HANDLE_T* const psHandle, U16* const pu16KeyMatrix)
{
    /* Parameter and initialization status check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (pu16KeyMatrix == NULL_D))
    {
        return HALKEY_ERROR_E;
    }
    
    *pu16KeyMatrix = psHandle->u16LastKeyMatrix;
    
    return HALKEY_OK_E;
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

//===========================================================================
// End of file.
//===========================================================================








