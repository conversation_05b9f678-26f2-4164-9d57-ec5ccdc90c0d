<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>8</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>416</x>
      <y>240</y>
      <w>712</w>
      <h>184</h>
    </coordinates>
    <panel_attributes>HalKey
--
+<PERSON><PERSON><PERSON>_eInit(psHandle:HALKEY_HANDLE_T* const, psConfig:const HALKEY_CONFIG_T* const):HALKEY_STATUS_E
+HalKey_eDeInit(psHandle:HALKEY_HANDLE_T* const):HALKEY_STATUS_E
+HalKey_eGetDefaultConfig(psConfig:HALKEY_CONFIG_T* const):HALKEY_STATUS_E
+HalKey_eScanMatrix(psHandle:HALKEY_HANDLE_T* const, pu16KeyMatrix:U16* const):HALKEY_STATUS_E
+Hal<PERSON>ey_eSetOutputLine(psHandle:const HALKEY_HANDLE_T* const, u8OutputLine:const U8, bState:const BOOL):HALKEY_STATUS_E
+HalKey_eReadInputLine(psHandle:const HALKEY_HANDLE_T* const, u8InputLine:const U8, pbState:BOOL* const):HALKEY_STATUS_E
+HalKey_eGetLastKeyMatrix(psHandle:const HALKEY_HANDLE_T* const, pu16KeyMatrix:U16* const):HALKEY_STATUS_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>88</x>
      <y>248</y>
      <w>248</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
HALKEY_CONFIG_T
--
+asInputPins[4]:HALKEY_GPIO_PIN_T
+asOutputPins[4]:HALKEY_GPIO_PIN_T
+u32ScanDelayUs:U32</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>88</x>
      <y>336</y>
      <w>248</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
HALKEY_HANDLE_T
--
+sConfig:HALKEY_CONFIG_T
+bInitialized:BOOL
+u16LastKeyMatrix:U16</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>504</x>
      <y>112</y>
      <w>496</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>HAL_GPIO
--
+HAL_GPIO_WritePin(GPIOx:GPIO_TypeDef*, GPIO_Pin:U16, PinState:GPIO_PinState):void
+HAL_GPIO_ReadPin(GPIOx:GPIO_TypeDef*, GPIO_Pin:U16):GPIO_PinState</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>168</y>
      <w>56</w>
      <h>88</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;90.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>568</x>
      <y>472</y>
      <w>368</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>Delay
--
+Delay_vDelayUs(u32DelayUs:U32):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>416</y>
      <w>56</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1168</x>
      <y>304</y>
      <w>216</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALKEY_STATUS_E
--
HALKEY_OK_E = 0
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1120</x>
      <y>312</y>
      <w>64</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>328</x>
      <y>256</y>
      <w>104</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;110.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>328</x>
      <y>344</y>
      <w>104</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;110.0;10.0</additional_attributes>
  </element>
</diagram>
