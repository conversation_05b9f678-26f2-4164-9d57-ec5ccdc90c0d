//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file HalWireComm.c
*
* @brief RS485 communication hardware abstraction layer implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "HalWireComm.h"
#include "usart.h"
#include "Utility.h"
#include <string.h>

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define HALWIRECOMM_DEFAULT_UART_HANDLE_D        (&huart3)                   /* Default UART handle */
#define HALWIRECOMM_DEFAULT_RS485_DIR_PIN_D      (RS485_DIR_Pin)             /* Default RS485 direction control pin */
#define HALWIRECOMM_DEFAULT_RS485_DIR_PORT_D     (RS485_DIR_GPIO_Port)       /* Default RS485 direction control port */
#define HALWIRECOMM_DIR_SWITCH_DELAY_US_D        10U                         /* RS485 direction switching delay time */

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize the RS485 communication hardware.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param psConfig [in]: Configuration parameters pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eInit(HALWIRECOMM_HANDLE_T* const psHandle, const HALWIRECOMM_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (psConfig == NULL_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Check configuration parameter validity */
    if ((psConfig->psUartHandle == NULL_D) || (psConfig->psRs485DirPort == NULL_D) || (psConfig->u16Rs485DirPin == 0U))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Save configuration parameters */
    psHandle->sConfig = *psConfig;
    
    /* Initialize other members of the handle */
    psHandle->eState = HALWIRECOMM_STATE_IDLE_E;
    psHandle->bTxComplete = FALSE_D;
    psHandle->bRxComplete = FALSE_D;
    psHandle->u16RxLength = 0U;
    psHandle->u16TxLength = 0U;
    memset(psHandle->au8RxBuffer, 0U, HALWIRECOMM_RX_BUFFER_SIZE_D);
    memset(psHandle->au8TxBuffer, 0U, HALWIRECOMM_TX_BUFFER_SIZE_D);
    
    /* Enable UART idle interrupt */
    __HAL_UART_ENABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_IDLE);

    /* Enable UART error interrupts */
    __HAL_UART_ENABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_ERR);
    
    psHandle->bInitialized = TRUE_D;
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Deinitialize the RS485 communication hardware.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eDeInit(HALWIRECOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if (psHandle == NULL_D) 
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    if (psHandle->bInitialized) 
    {
        /* Stop DMA transmission */
        HalWireComm_eStopTransfer(psHandle);
        
        /* Disable UART idle interrupt */
        __HAL_UART_DISABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_IDLE);
        
        /* Disable UART error interrupts */
        __HAL_UART_DISABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_ERR);
        
        /* Clear handle */
        psHandle->eState = HALWIRECOMM_STATE_IDLE_E;
        psHandle->bTxComplete = FALSE_D;
        psHandle->bRxComplete = FALSE_D;
        psHandle->u16RxLength = 0U;
        psHandle->u16TxLength = 0U;
        memset(psHandle->au8RxBuffer, 0U, HALWIRECOMM_RX_BUFFER_SIZE_D);
        memset(psHandle->au8TxBuffer, 0U, HALWIRECOMM_TX_BUFFER_SIZE_D);
        psHandle->bInitialized = FALSE_D;
    }

    return HALWIRECOMM_OK_E;
}

/**
* @brief Get the default configuration parameters.
* @remark None.
*
* @param psConfig [out]: Configuration structure pointer to return default configuration.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eGetDefaultConfig(HALWIRECOMM_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Set default configuration */
    psConfig->psUartHandle = HALWIRECOMM_DEFAULT_UART_HANDLE_D;
    psConfig->u16Rs485DirPin = HALWIRECOMM_DEFAULT_RS485_DIR_PIN_D;
    psConfig->psRs485DirPort = HALWIRECOMM_DEFAULT_RS485_DIR_PORT_D;
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Set the RS485 communication direction.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param eDirection [in]: Communication direction.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eSetDirection(const HALWIRECOMM_HANDLE_T* const psHandle, const HALWIRECOMM_DIRECTION_E eDirection)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Set RS485 direction pin state */
    GPIO_PinState ePinState = (eDirection == HALWIRECOMM_DIR_TX_E) ? GPIO_PIN_SET : GPIO_PIN_RESET;
    HAL_GPIO_WritePin(psHandle->sConfig.psRs485DirPort, psHandle->sConfig.u16Rs485DirPin, ePinState);
    
    /* Direction switching delay */
    Utility_vDelayUs(HALWIRECOMM_DIR_SWITCH_DELAY_US_D);
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Receive data.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eReceive(HALWIRECOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRECOMM_ERROR_E;
    }

    /* Stop DMA transmission */
    if (HalWireComm_eStopTransfer(psHandle) != HALWIRECOMM_OK_E)
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Set to receive mode */
    if (HalWireComm_eSetDirection(psHandle, HALWIRECOMM_DIR_RX_E) != HALWIRECOMM_OK_E)
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Set state */
    psHandle->eState = HALWIRECOMM_STATE_RX_E;
    
    /* Start DMA reception */
    if (HAL_UARTEx_ReceiveToIdle_DMA(psHandle->sConfig.psUartHandle, psHandle->au8RxBuffer, HALWIRECOMM_RX_BUFFER_SIZE_D) != HAL_OK)
    {
        return HALWIRECOMM_ERROR_E;
    }

    return HALWIRECOMM_OK_E;
}

/**
* @brief Transmit data.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eTransmit(HALWIRECOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (psHandle->u16TxLength == 0U) || (psHandle->u16TxLength > HALWIRECOMM_TX_BUFFER_SIZE_D))
    {
        return HALWIRECOMM_ERROR_E;
    }

    /* Stop DMA transmission */
    if (HalWireComm_eStopTransfer(psHandle) != HALWIRECOMM_OK_E)
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Set to transmit mode */
    if (HalWireComm_eSetDirection(psHandle, HALWIRECOMM_DIR_TX_E) != HALWIRECOMM_OK_E)
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Set state */
    psHandle->eState = HALWIRECOMM_STATE_TX_E;
    
    /* Start DMA transmission */
    if (HAL_UART_Transmit_DMA(psHandle->sConfig.psUartHandle, psHandle->au8TxBuffer, psHandle->u16TxLength) != HAL_OK)
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Stop bidirectional data transmission (including transmit and receive).
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eStopTransfer(HALWIRECOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Stop DMA transmission (including transmit and receive) */
    if (HAL_UART_DMAStop(psHandle->sConfig.psUartHandle) != HAL_OK)
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    /* Clear all related flags */
    psHandle->eState = HALWIRECOMM_STATE_IDLE_E;
    psHandle->bRxComplete = FALSE_D;
    psHandle->bTxComplete = FALSE_D;
    psHandle->u16RxLength = 0U;
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Get the current communication state.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param peState [out]: Current state pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eGetState(const HALWIRECOMM_HANDLE_T* const psHandle, HALWIRECOMM_STATE_E* const peState)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (peState == NULL_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    *peState = psHandle->eState;

    return HALWIRECOMM_OK_E;
}

/**
* @brief Check if transmission is complete.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param pbComplete [out]: Transmission complete status pointer (TRUE_D-completed, FALSE_D-not completed).
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eIsTxComplete(const HALWIRECOMM_HANDLE_T* const psHandle, BOOL* const pbComplete)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (pbComplete == NULL_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    *pbComplete = psHandle->bTxComplete;

    return HALWIRECOMM_OK_E;
}

/**
* @brief Check if reception is complete.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param pbComplete [out]: Reception complete status pointer (TRUE_D-completed, FALSE_D-not completed).
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eIsRxComplete(const HALWIRECOMM_HANDLE_T* const psHandle, BOOL* const pbComplete)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (pbComplete == NULL_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    *pbComplete = psHandle->bRxComplete;

    return HALWIRECOMM_OK_E;
}

/**
* @brief Get the length of received data.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param pu16Length [out]: Received data length pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eGetRxLength(const HALWIRECOMM_HANDLE_T* const psHandle, U16* const pu16Length)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (pu16Length == NULL_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    *pu16Length = psHandle->u16RxLength;

    return HALWIRECOMM_OK_E;
}

/**
* @brief Set the length of data to transmit.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param u16Length [in]: Data length.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eSetTxLength(HALWIRECOMM_HANDLE_T* const psHandle, const U16 u16Length)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (u16Length > HALWIRECOMM_TX_BUFFER_SIZE_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    psHandle->u16TxLength = u16Length;

    return HALWIRECOMM_OK_E;
}

/**
* @brief Get the receive buffer pointer.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param ppu8RxBuffer [out]: Receive buffer pointer address.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eGetRxBuffer(const HALWIRECOMM_HANDLE_T* const psHandle, U8** const ppu8RxBuffer)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (ppu8RxBuffer == NULL_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    *ppu8RxBuffer = (U8*)psHandle->au8RxBuffer;

    return HALWIRECOMM_OK_E;
}

/**
* @brief Get the transmit buffer pointer.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param ppu8TxBuffer [out]: Transmit buffer pointer address.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eGetTxBuffer(const HALWIRECOMM_HANDLE_T* const psHandle, U8** const ppu8TxBuffer)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (ppu8TxBuffer == NULL_D))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    *ppu8TxBuffer = (U8*)psHandle->au8TxBuffer;
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Clear the reception complete flag.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eClearRxComplete(HALWIRECOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    psHandle->bRxComplete = FALSE_D;
    psHandle->u16RxLength = 0U;
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Clear the transmission complete flag.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRECOMM_STATUS_E HalWireComm_eClearTxComplete(HALWIRECOMM_HANDLE_T* const psHandle)
{
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRECOMM_ERROR_E;
    }
    
    psHandle->bTxComplete = FALSE_D;
    
    return HALWIRECOMM_OK_E;
}

/**
* @brief Receive complete interrupt callback function (to be called in receive complete interrupt service routine).
* @remark None.
*
* @param pvUserData [in]: User data pointer (should be HALWIRECOMM_HANDLE_T pointer).
* @return Nothing.
*/
void HalWireComm_vRxCompleteCallback(void* const pvUserData)
{
    /* Parameter validity check */
    if (pvUserData == NULL_D)
    {
        return;
    }

    /* Convert user data to handle pointer */
    HALWIRECOMM_HANDLE_T* const psHandle = (HALWIRECOMM_HANDLE_T*)pvUserData;
    
    if ((psHandle->bInitialized) && (psHandle->eState == HALWIRECOMM_STATE_RX_E))
    {
        /* Get received data length */
        U16 u16RxLength = HALWIRECOMM_RX_BUFFER_SIZE_D - __HAL_DMA_GET_COUNTER(psHandle->sConfig.psUartHandle->hdmarx);

        if (u16RxLength > 0U)
        {
            /* Set received data length */
            psHandle->u16RxLength = u16RxLength;
            psHandle->bRxComplete = TRUE_D;
            psHandle->eState = HALWIRECOMM_STATE_IDLE_E;
        }
    }
}

/**
* @brief Transmit complete callback function (to be called in transmit complete interrupt service routine).
* @remark None.
*
* @param pvUserData [in]: User data pointer (should be HALWIRECOMM_HANDLE_T pointer).
* @return Nothing.
*/
void HalWireComm_vTxCompleteCallback(void* const pvUserData)
{
    /* Parameter validity check */
    if (pvUserData == NULL_D)
    {
        return;
    }

    /* Convert user data to handle pointer */
    HALWIRECOMM_HANDLE_T* const psHandle = (HALWIRECOMM_HANDLE_T*)pvUserData;
    
    if ((psHandle->bInitialized) && (psHandle->eState == HALWIRECOMM_STATE_TX_E))
    {
        /* Set transmission complete flag */
        psHandle->bTxComplete = TRUE_D;
        psHandle->eState = HALWIRECOMM_STATE_IDLE_E;
    }
}

/**
* @brief Error callback function (to be called in error interrupt service routine).
* @remark None.
*
* @param pvUserData [in]: User data pointer (should be HALWIRECOMM_HANDLE_T pointer).
* @return Nothing.
*/
void HalWireComm_vErrorOccurCallback(void* const pvUserData)
{
    /* Parameter validity check */
    if (pvUserData == NULL_D)
    {
        return;
    }

    /* Convert user data to handle pointer */
    HALWIRECOMM_HANDLE_T* const psHandle = (HALWIRECOMM_HANDLE_T*)pvUserData;
    
    /* Initialized status check */
    if (psHandle->bInitialized)
    {
        /* Set error state */
        psHandle->eState = HALWIRECOMM_STATE_ERROR_E;
    }
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

//===========================================================================
// End of file.
//===========================================================================








