//----------------------------------------------------------------------------
/**
* @file HalUi.h
* @remark HalUi public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef HALUI_H_
#define HALUI_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "main.h"
#include "GlobalTypes.h"
#include "lvgl/lvgl.h"
#include "lvgl/src/drivers/display/st7789/lv_st7789.h"
#include "FreeRTOS.h"
#include "task.h"
#include "cmsis_os.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------
/**
* @brief HAL layer UI module status enumeration
*/
typedef enum
{
    HALUI_OK_E = 0U,                    /* Operation successful */
    HALUI_ERROR_E,                      /* Operation failed/error */
    HALUI_TIMEOUT_E                     /* Operation timeout */
} HALUI_STATUS_E;

/**
* @brief LCD bus state enumeration
*/
typedef enum
{
    HALUI_LCD_BUS_IDLE_E = 0U,          /* LCD bus idle state */
    HALUI_LCD_BUS_BUSY_E                /* LCD bus busy state */
} HALUI_LCD_BUS_STATE_E;

/**
* @brief LCD display configuration structure
* @details Contains display configuration parameters for LCD display
*/
typedef struct
{
    U16 u16HRes;                        /* Horizontal resolution (pixels) */
    U16 u16VRes;                        /* Vertical resolution (pixels) */
    BOOL bColorInvert;                  /* Color invert flag (TRUE_D-inverted, FALSE_D-normal) */
    BOOL bAntiAliasing;                 /* Anti-aliasing flag (TRUE_D-enabled, FALSE_D-disabled) */
    lv_display_rotation_t eRotation;    /* Screen rotation angle (0°/90°/180°/270°) */
    U32 u32BufferDivisor;               /* Buffer size divisor (screen_size/divisor) */
    U32 u32DrawBufSize;                 /* LVGL display buffer size (calculated) */
} HALUI_LCD_DISPLAY_CONFIG_T;

/**
* @brief UI hardware configuration structure
* @details Contains all configuration parameters for UI hardware
*/
typedef struct
{
    SPI_HandleTypeDef* psSpiHandle;                 /* SPI handle pointer */
    U16 u16LcdCsPin;                                /* LCD chip select pin */
    GPIO_TypeDef* psLcdCsPort;                      /* LCD chip select port */
    U16 u16LcdDcxPin;                               /* LCD data/command pin */
    GPIO_TypeDef* psLcdDcxPort;                     /* LCD data/command port */
    U16 u16LcdResetPin;                             /* LCD reset pin */
    GPIO_TypeDef* psLcdResetPort;                   /* LCD reset port */
    HALUI_LCD_DISPLAY_CONFIG_T sLcdDisplayConfig;   /* LCD display configuration */
} HALUI_CONFIG_T;

/**
* @brief UI hardware handle structure
*/
typedef struct
{
    HALUI_CONFIG_T sConfig;                         /* Configuration parameter structure */
    volatile HALUI_LCD_BUS_STATE_E eLcdBusState;    /* LCD bus state (idle/busy) */
    lv_display_t* psLcdDisplay;                     /* LVGL display handle pointer */
    lv_color_t* psDrawBuf1;                         /* LVGL display buffer 1 pointer */
    lv_color_t* psDrawBuf2;                         /* LVGL display buffer 2 pointer */
    BOOL bInitialized;                              /* Initialization flag */
} HALUI_HANDLE_T;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
HALUI_STATUS_E HalUi_eInit(const HALUI_CONFIG_T* const psConfig);
void HalUi_vDeInit(void);
HALUI_STATUS_E HalUi_eGetDefaultConfig(HALUI_CONFIG_T* const psConfig);
HALUI_STATUS_E HalUi_eLcdIoInit(void);
HALUI_STATUS_E HalUi_eCreateLcdDisplay(void);
HALUI_STATUS_E HalUi_eIsLcdBusy(BOOL* const pbBusy);
HALUI_STATUS_E HalUi_eWaitLcdReady(const U32 u32TimeoutMs);
HALUI_STATUS_E HalUi_eGetLcdDisplay(lv_display_t** const ppsLcdDisplay);
void HalUi_vLcdColorTransferReadyCallback(SPI_HandleTypeDef* psSpiHandle);
void HalUi_vLcdSendCommand(lv_display_t* psDisp, const uint8_t* pu8Cmd, size_t szCmdSize, const uint8_t* pu8Param, size_t szParamSize);
void HalUi_vLcdSendColor(lv_display_t* psDisp, const uint8_t* pu8Cmd, size_t szCmdSize, uint8_t* pu8Param, size_t szParamSize);

#endif /* HALUI_H_ */

//===========================================================================
// End of file.
//===========================================================================








