/**
 * @remark 版权所有(C)2025 TRIED.
 * @remark 本软件受版权法保护，未经授权禁止复制、分发或修改。
 * @file Delay.h
 * @brief DWT定时器精确延时接口头文件
 * @details 基于ARM Cortex-M内核DWT定时器实现微秒级精确延时功能
 * <AUTHOR>
 * @date 2025-06-13
 * @note 适用于STM32F4系列微控制器
 */

#ifndef __DELAY_H
#define __DELAY_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "core_cm4.h"

/* Exported define -----------------------------------------------------------*/
#define DELAY_MAX_US	1000000UL     /* 最大微秒延时时间 */
#define DELAY_MAX_MS	1000UL        /* 最大毫秒延时时间 */

/* Exported types ------------------------------------------------------------*/

/* Exported constants --------------------------------------------------------*/

/* Exported macro ------------------------------------------------------------*/

/* Exported variables --------------------------------------------------------*/

/* Exported functions --------------------------------------------------------*/
/**
 * @brief 初始化DWT延时模块
 * @param u32SystemCoreClock 系统时钟频率（Hz）
 * @retval bool true-初始化成功，false-初始化失败
 */
bool Delay_bInit(uint32_t u32SystemCoreClock);

/**
 * @brief 反初始化DWT延时模块
 */
void Delay_vDeInit(void);

/**
 * @brief 微秒级精确延时
 * @param u32MicroSeconds 延时时间（微秒）
 */
void Delay_vDelayUs(uint32_t u32MicroSeconds);

/**
 * @brief 毫秒级延时
 * @param u32MilliSeconds 延时时间（毫秒）
 */
void Delay_vDelayMs(uint32_t u32MilliSeconds);

/**
 * @brief 获取当前DWT计数器值
 * @retval uint32_t 当前计数器值
 */
uint32_t Delay_u32GetCounter(void);

/**
 * @brief 计算两个计数器值之间的时间差（微秒）
 * @param u32StartCounter 起始计数器值
 * @param u32EndCounter 结束计数器值
 * @retval uint32_t 时间差（微秒）
 */
uint32_t Delay_u32GetTimeDiffUs(uint32_t u32StartCounter, uint32_t u32EndCounter);

#ifdef __cplusplus
}
#endif

#endif /* __DELAY_H */
