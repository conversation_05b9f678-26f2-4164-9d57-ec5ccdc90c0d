- [HalWirelessComm](#HalWirelessComm)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# HalWirelessComm

## 描述

`HalWirelessComm`作为无线通信的硬件抽象层。该模块主要负责无线通信的硬件操作，包括USART的初始化、DMA收发、中断处理、全双工通信，为上层应用提供稳定可靠的异步通信功能。支持连续接收模式和回调机制。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0064    |   4G与云端通信   |
| MNT0065 | 云端更新 |
| MNT0066 | 云端多设备组网数据上传 |
| MNT0067 | 蓝牙通信 |
| MNT0068 | 蓝牙更新               |
| MNT0069 | 蓝牙多设备组网数据上传 |
| MNT0070 | 远程通信加密 |

### 软件需求

1) 应能够实现用于无线通信的串口数据发送和接收功能的抽象
2) 应能够实现全双工通信，发送和接收操作相互独立
3) 应能够提供发送完成、接收完成、错误处理的回调机制
4) 应能够提供通信状态查询接口

### 假设

1) 硬件上UART已正确配置，包括波特率、数据位、停止位、校验位等参数。
2) DMA通道已在`main.h`中正确定义并完成配置。
3) UART空闲中断和错误中断已正确配置。

***

## 平台资源

 接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HAL_UARTEx_ReceiveToIdle_DMA()` | 启动DMA接收直到空闲中断 |
| `HAL_UART_Transmit_DMA()` | 启动DMA发送 |
| `HAL_UART_AbortReceive()` | 停止UART接收操作 |
| `HAL_UART_AbortTransmit()` | 停止UART发送操作 |
| `__HAL_UART_ENABLE_IT()` | 使能UART中断 |
| `__HAL_UART_DISABLE_IT()` | 禁用UART中断 |
| `__HAL_DMA_GET_COUNTER()` | 获取DMA剩余传输计数 |
| `memset()` | 内存清零操作 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用基于DMA的异步UART通信方案，支持全双工操作。该方案具有以下特点：

1. 使用DMA进行数据传输，CPU无需等待，提高系统效率。
2. 发送和接收操作完全独立，可以同时进行。
3. 使用UART空闲中断检测接收完成，支持变长数据接收。
4. 提供完整的回调机制处理收发和错误事件。
5. 支持通信状态监控和错误处理。
6. 回调函数采用通用签名`void (*pvCallback)(void *pvUserData)`，便于与统一的UART回调管理器集成。

### 静态设计

模块的静态设计如下所示：

![类图](Image/HalWirelessComm_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/HalWirelessComm_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：
   
   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `HalWirelessComm_eInit()` | 3      |
   | `HalWirelessComm_eDeInit()` | 5 |
   | `HalWirelessComm_eGetDefaultConfig()` | 2 |
   | `HalWirelessComm_eReceive()` | 4 |
   | `HalWirelessComm_eTransmit()` | 4 |
   | `HalWirelessComm_eGetTxState()` | 2 |
   | `HalWirelessComm_eGetRxState()` | 2 |
   | `HalWirelessComm_eGetRxLength()` | 2 |
   | `HalWirelessComm_eSetTxLength()` | 2 |
   | `HalWirelessComm_eGetTxBuffer()` | 2 |
   | `HalWirelessComm_eGetRxBuffer()` | 2 |
   | `HalWirelessComm_eGetContinuousRx()` | 2 |
   | `HalWirelessComm_eClearRxComplete()` | 3 |
   | `HalWirelessComm_eClearTxComplete()` | 3 |
   | `HalWirelessComm_vRxCompleteCallback()` | 4 |
   | `HalWirelessComm_vTxCompleteCallback()` | 3 |
   | `HalWirelessComm_vErrorOccurCallback()` | 3 |
   
2. 其他测试项：目前无免费工具，暂不列出。  

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用STM32CubeIDE搭建测试工程，硬件板卡为安富莱STM32-V5开发板。

   1.2 使用FreeRTOS新建StartWirelessCommTask任务，在其中实现HalWirelessComm模块的串口通信测试方案。任务调度时间为10ms。

2. 函数测试详细结果

   2.1 HalWirelessComm_eInit()

      2.1.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.2 分支2：配置参数空指针检测

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.3 分支3：UART句柄空指针检测

      - 测试用例：`psConfig->psUartHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测到无效UART句柄

      2.1.4 分支4：正常初始化

      - 测试用例：所有参数有效，UART句柄正确
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，句柄状态正确设置
      - 测试结果：通过，初始化成功，所有成员变量正确初始化

   2.2 HalWirelessComm_eDeInit()

      2.2.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.2.2 分支2：未初始化状态

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，直接成功
      - 测试结果：通过，函数跳过反初始化操作

      2.2.3 分支3：接收DMA停止失败

      - 测试用例：已初始化状态，HAL_UART_AbortReceive()返回HAL_ERROR
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确处理接收DMA停止失败

      2.2.4 分支4：发送DMA停止失败

      - 测试用例：已初始化状态，HAL_UART_AbortTransmit()返回HAL_ERROR
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确处理发送DMA停止失败

      2.2.5 分支5：正常反初始化

      - 测试用例：已初始化状态，DMA停止操作成功
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，句柄状态清零
      - 测试结果：通过，反初始化成功，中断禁用，所有状态正确清理

   2.3 HalWirelessComm_eGetDefaultConfig()

      2.3.1 分支1：空指针检测

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.3.2 分支2：正常获取默认配置

      - 测试用例：传入有效配置指针
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，配置参数正确设置
      - 测试结果：通过，默认配置正确设置：`psUartHandle = &huart2`, `bContinuousRx = TRUE_D`

   2.4 HalWirelessComm_eReceive()

      2.4.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.4.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.4.3 分支3：接收状态已激活

      - 测试用例：`eRxState = HALWIRELESSCOMM_COMM_BUSY_E`
      - 预期结果：返回 `HALWIRELESSCOMM_BUSY_E`
      - 测试结果：通过，函数正确返回忙碌状态

      2.4.4 分支4：正常启动接收

      - 测试用例：所有条件正常，DMA成功启动
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`eRxState = HALWIRELESSCOMM_COMM_BUSY_E`
      - 测试结果：通过，接收DMA成功启动

   2.5 HalWirelessComm_eTransmit()

      2.5.1 分支1：参数无效检测

      - 测试用例：传入 `psHandle = NULL_D` 或 `bInitialized = FALSE_D` 或 `u16TxLength = 0` 或 `u16TxLength > HALWIRELESSCOMM_TX_BUFFER_SIZE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测参数无效

      2.5.2 分支2：发送状态已激活

      - 测试用例：`eTxState = HALWIRELESSCOMM_COMM_BUSY_E`
      - 预期结果：返回 `HALWIRELESSCOMM_BUSY_E`
      - 测试结果：通过，函数正确返回忙碌状态

      2.5.3 分支3：DMA启动失败

      - 测试用例：HAL_UART_Transmit_DMA()返回HAL_ERROR
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确处理DMA启动失败

      2.5.4 分支4：正常启动发送

      - 测试用例：所有条件正常，DMA成功启动
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`eTxState = HALWIRELESSCOMM_COMM_BUSY_E`
      - 测试结果：通过，发送DMA成功启动

   2.6 HalWirelessComm_eGetTxState()

      2.6.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.6.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.6.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `peTxState = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.6.4 分支4：正常获取发送状态

      - 测试用例：有效参数，获取发送状态
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`*peTxState`正确反映状态
      - 测试结果：通过，正确返回发送状态

   2.7 HalWirelessComm_eGetRxState()

      2.7.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.7.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.7.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `peRxState = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.7.4 分支4：正常获取接收状态

      - 测试用例：有效参数，获取接收状态
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`*peRxState`正确反映状态
      - 测试结果：通过，正确返回接收状态

   2.8 HalWirelessComm_eGetRxLength()

      2.8.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.8.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.8.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `pu16Length = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.8.4 分支4：正常获取接收数据长度

      - 测试用例：有效参数，获取接收数据长度
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`*pu16Length`正确返回长度
      - 测试结果：通过，正确返回接收数据长度

   2.9 HalWirelessComm_eSetTxLength()

      2.9.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.9.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.9.3 分支3：长度超限检测

      - 测试用例：`u16Length > HALWIRELESSCOMM_TX_BUFFER_SIZE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测长度超限

      2.9.4 分支4：正常设置发送数据长度

      - 测试用例：有效参数，设置发送数据长度
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，长度正确设置
      - 测试结果：通过，发送数据长度设置成功

   2.10 HalWirelessComm_eGetTxBuffer()

      2.10.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.10.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.10.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `ppu8TxBuffer = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.10.4 分支4：正常获取发送缓冲区指针

      - 测试用例：有效参数，获取发送缓冲区指针
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`*ppu8TxBuffer`指向发送缓冲区
      - 测试结果：通过，正确返回发送缓冲区指针

   2.11 HalWirelessComm_eGetRxBuffer()

      2.11.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.11.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.11.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `ppu8RxBuffer = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.11.4 分支4：正常获取接收缓冲区指针

      - 测试用例：有效参数，获取接收缓冲区指针
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`*ppu8RxBuffer`指向接收缓冲区
      - 测试结果：通过，正确返回接收缓冲区指针

   2.12 HalWirelessComm_eGetContinuousRx()

      2.12.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.12.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.12.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `pbContinuousRx = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.12.4 分支4：正常获取连续接收模式标志

      - 测试用例：有效参数，获取连续接收模式状态
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`*pbContinuousRx`正确反映配置
      - 测试结果：通过，正确返回连续接收模式标志

   2.13 HalWirelessComm_eClearRxComplete()

      2.13.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.13.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.13.3 分支3：正常清除接收完成标志

      - 测试用例：有效参数，`eRxState = HALWIRELESSCOMM_COMM_COMPLETE_E`
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`eRxState = HALWIRELESSCOMM_COMM_IDLE_E`, `u16RxLength = 0`
      - 测试结果：通过，接收完成标志和长度正确清零

   2.14 HalWirelessComm_eClearTxComplete()

      2.14.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.14.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRELESSCOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.14.3 分支3：正常清除发送完成标志

      - 测试用例：有效参数，`eTxState = HALWIRELESSCOMM_COMM_COMPLETE_E`
      - 预期结果：返回 `HALWIRELESSCOMM_OK_E`，`eTxState = HALWIRELESSCOMM_COMM_IDLE_E`
      - 测试结果：通过，发送完成标志正确清零

   2.15 HalWirelessComm_vRxCompleteCallback()

      2.15.1 分支1：空指针检测

      - 测试用例：传入 `pvUserData = NULL_D`
      - 预期结果：函数直接返回，无操作
      - 测试结果：通过，函数安全返回

      2.15.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，无操作
      - 测试结果：通过，函数安全返回

      2.15.3 分支3：接收状态未激活

      - 测试用例：`eRxState != HALWIRELESSCOMM_COMM_BUSY_E`
      - 预期结果：函数直接返回，无操作
      - 测试结果：通过，函数跳过处理

      2.15.4 分支4：正常处理接收完成

      - 测试用例：有效参数，接收状态激活，有数据接收
      - 预期结果：设置接收完成状态，更新接收长度
      - 测试结果：通过，接收完成正确处理

   2.16 HalWirelessComm_vTxCompleteCallback()

      2.16.1 分支1：空指针检测

      - 测试用例：传入 `pvUserData = NULL_D`
      - 预期结果：函数直接返回，无操作
      - 测试结果：通过，函数安全返回

      2.16.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，无操作
      - 测试结果：通过，函数安全返回

      2.16.3 分支3：发送状态未激活

      - 测试用例：`eTxState != HALWIRELESSCOMM_COMM_BUSY_E`
      - 预期结果：函数直接返回，无操作
      - 测试结果：通过，函数跳过处理

      2.16.4 分支4：正常处理发送完成

      - 测试用例：有效参数，发送状态激活
      - 预期结果：设置发送完成状态
      - 测试结果：通过，发送完成正确处理

   2.17 HalWirelessComm_vErrorOccurCallback()

      2.17.1 分支1：空指针检测

      - 测试用例：传入 `pvUserData = NULL_D`
      - 预期结果：函数直接返回，无操作
      - 测试结果：通过，函数安全返回

      2.17.2 分支2：正常处理错误

      - 测试用例：有效参数，已初始化状态
      - 预期结果：设置接收和发送状态为错误状态
      - 测试结果：通过，错误状态正确设置


3. 集成测试详细结果

   3.1 串口回显功能集成测试

      3.1.1 测试目标

      验证HalWirelessComm模块在实际硬件环境下的串口收发数据的通信功能，通过回显测试确保数据收发的完整性和可靠性。该测试验证模块在真实场景中的使用，包括硬件交互、中断处理、DMA传输等完整功能。

      3.1.2 测试环境

      - 通信接口：USART1配置为115200波特率，8数据位，1停止位，无校验
      - DMA配置：DMA2_Stream2用于USART1_RX，DMA2_Stream7用于USART1_TX
      - 测试工具：PC端串口调试助手
   
      3.1.3 测试实现
   
      测试程序如下：
   
      ```c
      void StartWirelessCommTask(void const * argument)
      {
        /* USER CODE BEGIN StartWirelessCommTask */
        /* 注册回调函数到UartCallbackManager */
        UartCallbackManager_vAddIdleCallback(&huart1, HalWirelessComm_vRxCompleteCallback, &sHalWirelessCommHandle);
        UartCallbackManager_vAddTxCompleteCallback(&huart1, HalWirelessComm_vTxCompleteCallback, &sHalWirelessCommHandle);
        UartCallbackManager_vAddErrorCallback(&huart1, HalWirelessComm_vErrorOccurCallback, &sHalWirelessCommHandle);
      
        HALWIRELESSCOMM_CONFIG_T sConfig;
        HALWIRELESSCOMM_STATUS_E eStatus;
        HALWIRELESSCOMM_COMM_STATE_E eRxState = HALWIRELESSCOMM_COMM_IDLE_E;
        HALWIRELESSCOMM_COMM_STATE_E eTxState = HALWIRELESSCOMM_COMM_IDLE_E;
        U16 u16RxLength = 0U;
        U8* pu8RxBuffer = NULL_D;
        U8* pu8TxBuffer = NULL_D;
      
        /* 获取默认配置 */
        eStatus = HalWirelessComm_eGetDefaultConfig(&sConfig);
      
        /* 初始化HalWirelessComm模块 */
        eStatus = HalWirelessComm_eInit(&sHalWirelessCommHandle, &sConfig);
      
        /* 获取缓冲区指针 */
        HalWirelessComm_eGetRxBuffer(&sHalWirelessCommHandle, &pu8RxBuffer);
        HalWirelessComm_eGetTxBuffer(&sHalWirelessCommHandle, &pu8TxBuffer);
      
        /* 启动接收模式 */
        eStatus = HalWirelessComm_eReceive(&sHalWirelessCommHandle);
      
        /* Infinite loop */
        for(;;)
        {
      	/* 检查是否有数据接收完成 */
      	HalWirelessComm_eGetRxState(&sHalWirelessCommHandle, &eRxState);
      
      	if (eRxState == HALWIRELESSCOMM_COMM_COMPLETE_E)
      	{
      	  /* 获取接收到的数据长度 */
      	  HalWirelessComm_eGetRxLength(&sHalWirelessCommHandle, &u16RxLength);
      
      	  if (u16RxLength > 0U)
      	  {
      		/* 将接收到的数据复制到发送缓冲区 */
      		memcpy(pu8TxBuffer, pu8RxBuffer, u16RxLength);
      
      		/* 设置发送长度 */
      		HalWirelessComm_eSetTxLength(&sHalWirelessCommHandle, u16RxLength);
      
      		/* 清除接收完成标志 */
      		HalWirelessComm_eClearRxComplete(&sHalWirelessCommHandle);
      
      		/* 发送数据 */
      		eStatus = HalWirelessComm_eTransmit(&sHalWirelessCommHandle);
      
      		/* 等待发送完成 */
      		do
      		{
      		  HalWirelessComm_eGetTxState(&sHalWirelessCommHandle, &eTxState);
      		  osDelay(1);
      		} while (eTxState != HALWIRELESSCOMM_COMM_COMPLETE_E);
      
      		/* 清除发送完成标志 */
      		HalWirelessComm_eClearTxComplete(&sHalWirelessCommHandle);
      
      		/* 重新启动接收模式 */
      		HalWirelessComm_eReceive(&sHalWirelessCommHandle);
      	  }
      	  else
      	  {
      		/* 接收长度清除，清除标志并重新启动接收 */
      		HalWirelessComm_eClearRxComplete(&sHalWirelessCommHandle);
      		HalWirelessComm_eReceive(&sHalWirelessCommHandle);
      	  }
      	}
      
      	osDelay(10);
        }
        /* USER CODE END StartWirelessCommTask */
      }
      ```
   
      3.1.4 测试步骤
   
      - 通过串口调试助手发送各种测试数据，观察是否能收到相同的回显数据。
   
        通过串口调试助手向开发板发送数据，触发回调函数链表中对于接收数据回调函数的执行：
        
        ![测试图](Image/回调函数列表触发执行.png)
        
        ![测试图](Image/触发接收数据回调执行.png)
        
        执行接收数据回调函数之后，断点状态如下：
        
        ![测试图](Image/数据接收完成状态1.png)
        
        ![测试图](Image/数据接收完成状态2.png)
        
        测试程序向串口调试助手发送回显数据，触发回调函数链表中对于发送数据回调函数的执行：
        
        ![测试图](Image/触发发送数据回调执行.png)
        
        执行发送数据回调函数之后，断点状态如下：
        
        ![测试图](Image/数据发送完成状态1.png)
        
        ![测试图](Image/数据发送完成状态2.png)
        
        整体测试效果如下：
        
        ![测试图](Image/串口调试工具回显测试.png)
   
   测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

无

### 可重用性声明

1. 该模块依赖于STM32 HAL库和GlobalTypes类型定义。
2. 该模块需要正确配置的UART外设和DMA通道。
4. 移植到其他平台时需要适配相应的UART和DMA驱动接口。 