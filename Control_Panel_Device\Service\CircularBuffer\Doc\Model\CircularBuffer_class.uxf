<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>8</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>400</x>
      <y>176</y>
      <w>488</w>
      <h>320</h>
    </coordinates>
    <panel_attributes>CircularBuffer
--

--
+CircularBuffer_eInit(psBuffer: CIRCULARBUFFER_T*, 
                      pu8BufferMem: uint8_t*, 
                      u16Size: uint16_t): CIRCULARBUFFER_STATUS_E
+CircularBuffer_eDeInit(psBuffer: CIRCULARBUFFER_T*): CIRCULARBUFFER_STATUS_E
+CircularBuffer_ePutByte(psBuffer: CIRCULARBUFFER_T*, 
                         u8Data: uint8_t): CIRCULARBUFFER_STATUS_E
+CircularBuffer_eGetByte(psBuffer: CIRCULARBUFFER_T*, 
                         pu8Data: uint8_t*): CIRCULARBUFFER_STATUS_E
+CircularBuffer_u16PutBytes(psBuffer: CIRCULARBUFFER_T*, 
                            pu8Data: const uint8_t*, 
                            u16Length: uint16_t): uint16_t
+CircularBuffer_u16GetBytes(psBuffer: CIRCULARBUFFER_T*, 
                            pu8Data: uint8_t*, 
                            u16Length: uint16_t): uint16_t
+CircularBuffer_u16GetCount(psBuffer: const CIRCULARBUFFER_T*): uint16_t
+CircularBuffer_u16GetFreeSpace(psBuffer: const CIRCULARBUFFER_T*): uint16_t
+CircularBuffer_bIsEmpty(psBuffer: const CIRCULARBUFFER_T*): bool
+CircularBuffer_bIsFull(psBuffer: const CIRCULARBUFFER_T*): bool
+CircularBuffer_eClear(psBuffer: CIRCULARBUFFER_T*): CIRCULARBUFFER_STATUS_E
+CircularBuffer_u16Peek(psBuffer: const CIRCULARBUFFER_T*, 
                        pu8Data: uint8_t*, 
                        u16Length: uint16_t): uint16_t</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>80</x>
      <y>176</y>
      <w>240</w>
      <h>128</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
CIRCULARBUFFER_STATUS_E
--
CIRCULARBUFFER_OK = 0
CIRCULARBUFFER_ERROR
CIRCULARBUFFER_FULL
CIRCULARBUFFER_EMPTY
CIRCULARBUFFER_INVALID_PARAM</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>936</x>
      <y>176</y>
      <w>224</w>
      <h>144</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
CIRCULARBUFFER_T
--
+pu8Buffer: uint8_t*
+u16Size: uint16_t
+u16Head: volatile uint16_t
+u16Tail: volatile uint16_t
+u16Count: volatile uint16_t
+bInitialized: bool</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>400</x>
      <y>56</y>
      <w>304</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>&lt;&lt;constants&gt;&gt;
Configuration
--
CIRCULARBUFFER_DEFAULT_SIZE = 256
CIRCULARBUFFER_MAX_SIZE = 4096</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>160</x>
      <y>416</y>
      <w>160</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>&lt;&lt;external&gt;&gt;
Standard Libraries
--
stdint.h
stdbool.h
string.h</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>312</x>
      <y>176</y>
      <w>104</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
uses</panel_attributes>
    <additional_attributes>10.0;20.0;110.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>880</x>
      <y>176</y>
      <w>72</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
uses</panel_attributes>
    <additional_attributes>70.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>544</x>
      <y>112</y>
      <w>48</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
uses</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;80.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>312</x>
      <y>416</y>
      <w>104</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
requires</panel_attributes>
    <additional_attributes>10.0;20.0;110.0;20.0</additional_attributes>
  </element>
</diagram>
