- [HalUi](#HalUi)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# HalUi

## 描述

LCD用户界面硬件抽象层实现文件。实现基于ST7789芯片的LCD显示屏硬件操作功能，包括SPI通信、显示控制、LVGL驱动接口等底层硬件抽象。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0001    |   上电界面   |
| MNT0002 | 界面分区 |
| MNT0003 | 顶部区域内容 |
| MNT0004 | 顶部区域高度 |
| MNT0005 | 底部区域内容 |
| MNT0006 | 底部区域高度 |
| MNT0007 | 主界面的页面显示 |
| MNT0008 | 主界面的数据显示样式 |
| MNT0009 | 主界面添加与删除新页面   |
| MNT0010 | 主界面不同页面的切换显示 |

### 软件需求

1) 应能够实现屏幕的文字、图形显示
2) 应能够实现对屏幕的背光、对比度的调节
3) 应能够实现对LCD的Ui显示功能的抽象

### 假设

除了列出的需求外，还需以下假设:

1) 硬件平台为STM32F4系列微控制器。
2) LCD控制器为ST7789。
3) 使用`hspi3`与LCD进行通信。
4) 使用FreeRTOS或其他兼容的操作系统以支持`osDelay`等函数。
5) LCD使用SPI接口，具有CS、DCX、RESET控制引脚。
6) 支持DMA传输以提高颜色数据传输效率。

***

## 平台资源

 接口是组件定义变频器系统功能的提供和使用的“契约”接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `lvgl`图形库 | 用于UI开发的高级图形库。 |
| `STM32 HAL Library` | STM32的硬件抽象库，用于SPI和GPIO操作。 |
| `FreeRTOS (CMSIS-OS)` | 用于任务延时和时间管理。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块封装了硬件初始化的具体步骤和数据传输的细节，并为LVGL图形库提供了标准的驱动接口。该模块采用单例模式，使用全局静态句柄`sSingletonHandle`管理所有硬件状态和配置信息。

### 静态设计

![](Image/HalUi_class.png)

### 动态设计

![](Image/HalUi_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：
   
   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `HalUi_eInit()` | 6       |
   | `HalUi_vDeInit()` | 4 |
   | `HalUi_eGetDefaultConfig()` | 2 |
   | `HalUi_eLcdIoInit()` | 2 |
   | `HalUi_eCreateLcdDisplay()` | 6 |
   | `HalUi_eIsLcdBusy()` | 2 |
   | `HalUi_eWaitLcdReady()` | 4 |
   | `HalUi_eGetLcdDisplay()` | 2 |
   | `HalUi_vLcdColorTransferReadyCallback()` | 3 |
   | `HalUi_vLcdSendCommand()` | 4 |
   | `HalUi_vLcdSendColor()` | 4 |
   
2. 其他测试项：目前无免费工具，暂不列出。  

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用STM32CubeIDE搭建测试工程，硬件板卡为基于STM32F407IGTx微控制器的安富莱STM32-V5开发板。

   1.2 使用FreeRTOS新建StartUiTask任务，在其中实现HalUi模块的LCD显示测试方案。软件使用LVGL 9.2.2图形库。屏幕为ST7789驱动的240x320像素LCD显示屏。通信接口为SPI3总线，使用DMA传输。任务调度时间为5ms。

2. 函数测试详细结果

   2.1 HalUi_eInit()

      2.1.1 分支1：空指针检测

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.2 分支2：SPI句柄空指针检测

      - 测试用例：传入 `psConfig->psSpiHandle = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.3 分支3：LCD CS端口空指针检测

      - 测试用例：传入 `psConfig->psLcdCsPort = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.4 分支4：LCD DCX端口空指针检测

      - 测试用例：传入 `psConfig->psLcdDcxPort = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.5 分支5：LCD Reset端口空指针检测

      - 测试用例：传入 `psConfig->psLcdResetPort = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.6 分支6：LCD CS引脚无效值检测

      - 测试用例：传入 `psConfig->u16LcdCsPin = 0U`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.7 分支7：LCD DCX引脚无效值检测

      - 测试用例：传入 `psConfig->u16LcdDcxPin = 0U`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.8 分支8：LCD Reset引脚无效值检测

      - 测试用例：传入 `psConfig->u16LcdResetPin = 0U`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.9 分支9：水平分辨率无效值检测

      - 测试用例：传入 `psConfig->sLcdDisplayConfig.u16HRes = 0U`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.10 分支10：垂直分辨率无效值检测

      - 测试用例：传入 `psConfig->sLcdDisplayConfig.u16VRes = 0U`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.11 分支11：缓冲区分割参数为0检测

      - 测试用例：传入 `psConfig->sLcdDisplayConfig.u32BufferDivisor = 0U`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.12 分支12：缓冲区分割参数超限检测

      - 测试用例：传入 `psConfig->sLcdDisplayConfig.u32BufferDivisor = 101U`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.13 分支13：LCD IO初始化失败

      - 测试用例：模拟`HalUi_eLcdIoInit()`返回`HALUI_ERROR_E`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.14 分支14：正常初始化

      - 测试用例：所有参数有效，GPIO配置正确
      - 预期结果：返回 `HALUI_OK_E`，`bInitialized = TRUE_D`
      - 测试结果：通过，初始化成功

   2.2 HalUi_vDeInit()

      2.2.1 分支1：未初始化状态

      - 测试用例：未初始化状态，`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确处理未初始化状态

      2.2.2 分支2：缓冲区1为空

      - 测试用例：已初始化状态，`psDrawBuf1 = NULL_D`
      - 预期结果：跳过缓冲区1释放操作
      - 测试结果：通过，函数正确跳过空指针操作

      2.2.3 分支3：缓冲区2为空

      - 测试用例：已初始化状态，`psDrawBuf2 = NULL_D`
      - 预期结果：跳过缓冲区2释放操作
      - 测试结果：通过，函数正确跳过空指针操作

      2.2.4 分支4：正常去初始化

      - 测试用例：已初始化状态，所有资源已分配
      - 预期结果：释放所有资源，重置句柄，`bInitialized = FALSE_D`
      - 测试结果：通过，去初始化成功

   2.3 HalUi_eGetDefaultConfig()

      2.3.1 分支1：空指针检测

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.3.2 分支2：正常获取默认配置

      - 测试用例：传入有效配置指针
      - 预期结果：返回 `HALUI_OK_E`，配置参数正确设置
      - 测试结果：通过，所有默认配置参数正确设置

   2.4 HalUi_eLcdIoInit()

      2.4.1 分支1：SPI回调注册失败

      - 测试用例：HAL_SPI_RegisterCallback返回HAL_ERROR
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确处理回调注册失败

      2.4.2 分支2：正常IO初始化

      - 测试用例：所有HAL函数调用成功
      - 预期结果：返回 `HALUI_OK_E`，完成LCD复位和GPIO初始化
      - 测试结果：通过，IO初始化成功

   2.5 HalUi_eCreateLcdDisplay()

      2.5.1 分支1：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.5.2 分支2：显示器已创建

      - 测试用例：`psLcdDisplay != NULL_D`
      - 预期结果：返回 `HALUI_OK_E`，不重复创建
      - 测试结果：通过，函数正确处理重复创建

      2.5.3 分支3：显示器创建失败

      - 测试用例：lv_st7789_create返回NULL_D
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确处理创建失败

      2.5.4 分支4：缓冲区1分配失败

      - 测试用例：lv_malloc返回NULL_D（第一次调用）
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确处理内存分配失败

      2.5.5 分支5：缓冲区2分配失败

      - 测试用例：lv_malloc返回NULL_D（第二次调用）
      - 预期结果：返回 `HALUI_ERROR_E`，释放缓冲区1
      - 测试结果：通过，函数正确处理内存分配失败并清理资源

      2.5.6 分支6：正常创建显示器，分配缓冲区

      - 测试用例：所有操作成功
      - 预期结果：返回 `HALUI_OK_E`，完成显示器配置
      - 测试结果：通过，显示器创建成功，分配缓冲区

   2.6 HalUi_eIsLcdBusy()

      2.6.1 分支1：空指针检测

      - 测试用例：传入 `pbBusy = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.6.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.6.3 分支3：LCD总线空闲

      - 测试用例：`eLcdBusState = HALUI_LCD_BUS_IDLE_E`
      - 预期结果：返回 `HALUI_OK_E`，`*pbBusy = FALSE_D`
      - 测试结果：通过，函数正确返回空闲状态

      2.6.4 分支4：LCD总线忙碌

      - 测试用例：`eLcdBusState = HALUI_LCD_BUS_BUSY_E`
      - 预期结果：返回 `HALUI_OK_E`，`*pbBusy = TRUE_D`
      - 测试结果：通过，函数正确返回忙碌状态

   2.7 HalUi_eWaitLcdReady()

      2.7.1 分支1：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.7.2 分支2：等待超时

      - 测试用例：LCD总线持续忙碌，超过超时时间
      - 预期结果：返回 `HALUI_TIMEOUT_E`
      - 测试结果：通过，函数正确处理超时情况

      2.7.3 分支3：等待成功

      - 测试用例：LCD总线在超时时间内变为空闲
      - 预期结果：返回 `HALUI_OK_E`
      - 测试结果：通过，等待功能正常工作

   2.8 HalUi_eGetLcdDisplay()

      2.8.1 分支1：空指针检测

      - 测试用例：传入 `ppsLcdDisplay = NULL_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.8.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALUI_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.8.3 分支3：正常获取显示器句柄

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALUI_OK_E`，`*ppsLcdDisplay = psLcdDisplay`
      - 测试结果：通过，正确返回显示器句柄

   2.9 HalUi_vLcdColorTransferReadyCallback()

      2.9.1 分支1：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确处理未初始化状态

      2.9.2 分支2：显示器句柄为空

      - 测试用例：`psLcdDisplay = NULL_D`
      - 预期结果：跳过LVGL通知操作
      - 测试结果：通过，函数正确跳过空指针操作

      2.9.3 分支3：正常回调处理

      - 测试用例：系统已初始化，显示器句柄有效
      - 预期结果：设置CS高电平，清除忙碌状态，通知LVGL
      - 测试结果：通过，回调功能正常工作

   2.10 HalUi_vLcdSendCommand()

      2.10.1 分支1：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确处理未初始化状态

      2.10.2 分支2：SPI命令传输失败

      - 测试用例：HAL_SPI_Transmit返回HAL_ERROR
      - 预期结果：不设置DCX高电平，跳过参数传输，不设置CS高电平
      - 测试结果：通过，函数正确处理传输失败

      2.10.3 分支3：正常命令发送

      - 测试用例：所有SPI操作成功
      - 预期结果：完成命令和参数发送，正确控制GPIO
      - 测试结果：通过，命令发送功能正常工作

   2.11 HalUi_vLcdSendColor()

      2.11.1 分支1：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确处理未初始化状态

      2.11.2 分支2：SPI命令传输失败

      - 测试用例：HAL_SPI_Transmit返回HAL_ERROR
      - 预期结果：跳过颜色数据传输
      - 测试结果：通过，函数正确处理传输失败

      2.11.3 分支3：正常颜色数据发送

      - 测试用例：所有SPI操作成功
      - 预期结果：完成命令发送，设置忙碌状态，启动DMA传输
      - 测试结果：通过，颜色数据发送功能正常工作

3. 集成测试

   3.1 初始化流程测试

   - 测试步骤：
     - 配置获取：通过`HalUi_eGetDefaultConfig()`获取默认硬件配置参数
     - LVGL初始化：调用`lv_init()`初始化图形库
     - 硬件初始化：执行`HalUi_eInit()`配置SPI接口、GPIO控制引脚
     - 显示创建：通过`HalUi_eCreateLcdDisplay()`创建ST7789显示驱动实例
     - 缓冲区分配：自动分配双缓冲区，大小为屏幕面积的1/10
   - 测试结果：通过，所有初始化步骤成功完成，LCD显示器正常工作

   3.2 界面显示测试

   - 测试程序创建了一个黑色背景的测试界面，包含以下UI元素：
     - 标题标签: 显示"HalUi Test"，黑色字体，位于屏幕顶部
     - 状态标签: 显示"LCD Init OK"，橙色字体，指示初始化状态
     - 计数器标签: 显示"Count: X"，青色字体， 每1秒（200个周期）更新一次计数显示
     - 进度条: 蓝色指示器，尺寸180x20像素，每100ms（20个周期）递增1%，0-100%循环显示
     - 彩色矩形: 60x30像素的颜色测试块，循环显示6种颜色，每2秒（400个周期）切换颜色矩形
   - 测试结果：通过，所有UI元素正确显示，颜色准确，动画流畅

   ![测试图](Image/LCD测试显示.png)

测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

下面的列表概述了在以后可以对设计进行改进的点:

1. 支持更多LCD控制器类型（如ILI9341等）
2. 添加LCD刷新频率配置选项
3. 优化等待机制，减少轮询延时

### 可重用性声明

1. 适用于所有使用STM32F4系列微控制器并通过SPI接口连接ST7789 LCD控制器的平台。
2. 依赖于STM32 HAL库和FreeRTOS。
3. 需要适配具体的GPIO引脚配置和SPI外设配置。
4. LVGL版本需要支持ST7789驱动接口。 
