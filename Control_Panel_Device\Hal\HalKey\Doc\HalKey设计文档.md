- [<PERSON><PERSON><PERSON>](#<PERSON><PERSON><PERSON>)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# HalKey

## 描述

`HalKey`作为按键输入的硬件抽象层。该模块主要负责4x4矩阵按键的硬件操作，包括GPIO的初始化与控制、矩阵扫描算法的实现，为上层应用提供稳定可靠的按键状态。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0075    |   左软键功能   |
| MNT0076 | 右软键功能 |
| MNT0077 | 方向键功能 |
| MNT0078 | 启动键功能 |
| MNT0079 | 停止键功能 |
| MNT0080 | 帮助键功能 |
| MNT0081 | 控制权切换功能 |
| MNT0082 | 左方向键+右方向键 |
| MNT0083 | 左方向键+上方向键 |
| MNT0084 | 左方向键+下方向键 |
| MNT0085 | 右方向键+上方向键 |
| MNT0086 | 右方向键+下方向键 |
| MNT0087 | 左方向键+帮助键   |

### 软件需求

1) 应能够实现对按键按下的检测，包括短按、长按、组合
2) 应能够实现对按键扫描功能的抽象

### 假设

1) 硬件上连接的是一个标准的4x4矩阵键盘。
2) GPIO引脚已在`main.h`中正确定义并完成时钟配置。
3) 输入引脚（行线）配置为上拉输入模式。
4) 输出引脚（列线）配置为推挽输出模式。
5) 按键按下时，对应的行线和列线短接。

***

## 平台资源

 接口是组件定义变频器系统功能的提供和使用的“契约”接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HAL_GPIO_WritePin()` | 设置GPIO引脚的输出电平（高/低）。 |
| `HAL_GPIO_ReadPin()` | 读取GPIO引脚的输入电平。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用经典的矩阵键盘扫描方法。该方法通过逐列（或逐行）施加低电平，然后读取所有行（或列）的电平状态来确定哪个按键被按下。具体步骤如下：

1. 将所有输出线（列）设置为高电平。
2. 逐个将某一列设置为低电平。
3. 读取所有输入线（行）的状态。如果某一行也为低电平，则说明该行与该列交叉处的按键被按下。
4. 重复步骤2和3，直到扫描完所有列。

通过这种方式，可以用较少的I/O口（M+N个）控制大量的按键（M*N个）。

### 静态设计

模块的静态设计如下所示：

![类图](Image/HalKey_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/HalKey_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：
   
   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `HalKey_eInit()` | 9       |
   | `HalKey_eDeInit()` | 5 |
   | `HalKey_eGetDefaultConfig()` | 2 |
   | `HalKey_eScanMatrix()` | 10 |
   | `HalKey_eSetOutputLine()` | 3 |
   | `HalKey_eReadInputLine()` | 2 |
   | `HalKey_eGetLastKeyMatrix()` | 2 |
   
2. 其他测试项：目前无免费工具，暂不列出。  

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用STM32CubeIDE搭建测试工程，硬件板卡为安富莱STM32-V5开发板。

   1.2 使用FreeRTOS新建StartKeyTask任务，在其中实现HalKey模块的4×4按键矩阵扫描测试方案。任务调度时间为5ms。

2. 函数测试详细结果

   2.1 HalKey_eInit()

      2.1.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.2 分支2：配置参数空指针检测

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.3 分支3：输入引脚配置检测

      - 测试用例：输入引脚GPIO端口为NULL_D或引脚号为0
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测到无效配置

      2.1.4 分支4：输出引脚配置检测

      - 测试用例：输出引脚GPIO端口为NULL_D或引脚号为0
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测到无效配置

      2.1.5 分支5：扫描延时参数检测

      - 测试用例：`u32ScanDelayUs = 0`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测到无效延时参数

      2.1.6 分支6：输出线设置失败

      - 测试用例：GPIO配置错误导致输出线设置失败
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确处理设置失败情况

      2.1.7 分支7：正常初始化

      - 测试用例：所有参数有效，GPIO配置正确
      - 预期结果：返回 `HALKEY_OK_E`，句柄状态正确设置
      - 测试结果：通过，初始化成功

   2.2 HalKey_eDeInit()

      2.2.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.2.2 分支2：未初始化状态

      - 测试用例：未初始化状态，`bInitialized = FALSE_D`
      - 预期结果：返回 `HALKEY_OK_E`，直接成功
      - 测试结果：通过，函数跳过反初始化操作

      2.2.3 分支3：输出线恢复失败

      - 测试用例：已初始化状态，GPIO设置失败
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.2.4 分支4：正常反初始化

      - 测试用例：已初始化状态，GPIO操作正常
      - 预期结果：返回 `HALKEY_OK_E`，句柄状态清零
      - 测试结果：通过，反初始化成功

   2.3 HalKey_eGetDefaultConfig()

      2.3.1 分支1：空指针检测

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.3.2 分支2：正常获取默认配置

      - 测试用例：传入有效配置指针
      - 预期结果：返回 `HALKEY_OK_E`，配置参数正确设置
      - 测试结果：通过，所有默认配置参数正确设置

   2.4 HalKey_eScanMatrix()

      2.4.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.4.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.4.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `pu16KeyMatrix = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.4.4 分支4：输出线设置失败

      - 测试用例：GPIO设置操作失败
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确处理GPIO设置失败

      2.4.5 分支5：输入线读取失败

      - 测试用例：GPIO读取操作失败
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确处理GPIO读取失败

      2.4.6 分支6：输出线恢复失败

      - 测试用例：最后恢复输出线时GPIO操作失败
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确处理恢复失败

      2.4.7 分支7：正常扫描（无按键按下）

      - 函数调用：`HalKey_eScanMatrix(&sHalKeyHandle, &u16CurrentKeyMatrix)`
      - 变量说明：使用变量`u16CurrentKeyMatrix`获取当前按键扫描结果
      - 测试用例：所有输入线为高电平
      - 预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0000`
      - 测试结果：通过，扫描结果正确

      ![测试图](Image/无按键触发.png)

      2.4.8 分支8：正常扫描（所有16个按键的单独测试）

      - 函数调用：`HalKey_eScanMatrix(&sHalKeyHandle, &u16CurrentKeyMatrix)`
      - 变量说明：使用变量`u16CurrentKeyMatrix`获取当前按键扫描结果
      - 测试用例和预期结果：
        - 按键0触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0001` 
        - 按键1触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0002` 
        - 按键2触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0004` 
        - 按键3触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0008` 
        - 按键4触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0010` 
        - 按键5触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0020` 
        - 按键6触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0040` 
        - 按键7触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0080` 
        - 按键8触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0100` 
        - 按键9触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0200` 
        - 按键10触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0400` 
        - 按键11触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0800` 
        - 按键12触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x1000` 
        - 按键13触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x2000` 
        - 按键14触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x4000` 
        - 按键15触发，预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x8000` 
      - 测试结果：通过，返回 `HALKEY_OK_E`，单个按键扫描结果正确，测试用例的测试图如下：

      ![测试图](Image/按键0触发.png)
      ![测试图](Image/按键1触发.png)
      ![测试图](Image/按键2触发.png)
      ![测试图](Image/按键3触发.png)
      ![测试图](Image/按键4触发.png)
      ![测试图](Image/按键5触发.png)
      ![测试图](Image/按键6触发.png)
      ![测试图](Image/按键7触发.png)
      ![测试图](Image/按键8触发.png)
      ![测试图](Image/按键9触发.png)
      ![测试图](Image/按键10触发.png)
      ![测试图](Image/按键11触发.png)
      ![测试图](Image/按键12触发.png)
      ![测试图](Image/按键13触发.png)
      ![测试图](Image/按键14触发.png)
      ![测试图](Image/按键15触发.png)

      2.4.9 分支9：正常扫描（多个按键按下）

      - 函数调用：`HalKey_eScanMatrix(&sHalKeyHandle, &u16CurrentKeyMatrix)`
      - 测试用例：按键0和按键5同时按下，使用变量`u16CurrentKeyMatrix`获取当前按键扫描结果
      - 预期结果：返回 `HALKEY_OK_E`，`u16CurrentKeyMatrix = 0x0021`
      - 测试结果：通过，多按键扫描结果正确，测试用例的测试图结果如下：

   ![测试图](Image/按键0和按键5触发.png)

   2.5 HalKey_eSetOutputLine()

      2.5.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.5.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.5.3 分支3：输出线号超出范围

      - 测试用例：`u8OutputLine >= 4`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测参数范围

      2.5.4 分支4：设置高电平

      - 测试用例：`bState = TRUE_D`
      - 预期结果：返回 `HALKEY_OK_E`，GPIO设置为高电平
      - 测试结果：通过，所有输出线高电平设置成功

      2.5.5 分支5：设置低电平

      - 测试用例：`bState = FALSE_D`
      - 预期结果：返回 `HALKEY_OK_E`，GPIO设置为低电平
      - 测试结果：通过，所有输出线低电平设置成功

   2.6 HalKey_eReadInputLine()

      2.6.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.6.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.6.3 分支3：输入线号超出范围

      - 测试用例：`u8InputLine >= 4`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测参数范围

      2.6.4 分支4：空指针检测（输出参数）

      - 测试用例：传入 `pbState = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.6.5 分支5：读取高电平

      - 测试用例：GPIO引脚为高电平
      - 预期结果：返回 `HALKEY_OK_E`，`*pbState = TRUE_D`
      - 测试结果：通过，所有输入线高电平读取正确

      2.6.6 分支6：读取低电平

      - 测试用例：GPIO引脚为低电平
      - 预期结果：返回 `HALKEY_OK_E`，`*pbState = FALSE_D`
      - 测试结果：通过，所有输入线低电平读取正确

   2.7 HalKey_eGetLastKeyMatrix()

      2.7.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.7.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.7.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `pu16KeyMatrix = NULL_D`
      - 预期结果：返回 `HALKEY_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.7.4 分支4：正常获取最后扫描结果

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALKEY_OK_E`，`*pu16KeyMatrix = u16LastKeyMatrix`
      - 测试结果：通过，正确返回最后扫描结果


测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

1. 中断模式：目前采用轮询方式扫描，会持续占用CPU时间。未来可改进为中断模式，当有按键按下时，通过行线或列线的电平变化触发外部中断，再进行扫描，以降低CPU占用率和功耗。
2. 防抖功能：目前仅通过硬件延时保证GPIO稳定，未来可增加软件防抖算法，提高按键检测的可靠性。

### 可重用性声明

1. 该模块依赖于STM32 HAL库和GlobalTypes类型定义。
2. 该模块依赖于Delay模块提供的微秒级延时功能。