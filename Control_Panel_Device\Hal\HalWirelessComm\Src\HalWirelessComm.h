//----------------------------------------------------------------------------
/**
* @file HalWirelessComm.h
* @remark HalWirelessComm public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef HALWIRELESSCOMM_H_
#define HALWIRELESSCOMM_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "main.h"
#include "GlobalTypes.h"
#include "Utility.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------
#define HALWIRELESSCOMM_RX_BUFFER_SIZE_D     1024U       /* Receive buffer size */
#define HALWIRELESSCOMM_TX_BUFFER_SIZE_D     1024U       /* Transmit buffer size */

/**
* @brief External USART communication operation status enumeration
*/
typedef enum
{
    HALWIRELESSCOMM_OK_E = 0U,                   /* Operation successful */
    HALWIRELESSCOMM_ERROR_E,                     /* Operation failed */
    HALWIRELESSCOMM_BUSY_E                       /* Device busy */
} HALWIRELESSCOMM_STATUS_E;

/**
* @brief Communication status enumeration (used for both transmit and receive)
*/
typedef enum
{
    HALWIRELESSCOMM_COMM_IDLE_E = 0U,            /* Communication idle/ready */
    HALWIRELESSCOMM_COMM_BUSY_E,                 /* Communication in progress */
    HALWIRELESSCOMM_COMM_COMPLETE_E,             /* Communication completed successfully */
    HALWIRELESSCOMM_COMM_ERROR_E                 /* Communication error occurred */
} HALWIRELESSCOMM_COMM_STATE_E;

/**
* @brief External USART communication configuration structure
* @details Contains all configuration parameters for USART communication
*/
typedef struct
{
    UART_HandleTypeDef* psUartHandle;       /* UART handle pointer */
    BOOL bContinuousRx;                     /* Enable continuous receive mode (TRUE_D-enable, FALSE_D-disable) */
} HALWIRELESSCOMM_CONFIG_T;

/**
* @brief External USART communication handle structure
*/
typedef struct
{
    HALWIRELESSCOMM_CONFIG_T sConfig;                       /* Configuration parameters structure */
    volatile HALWIRELESSCOMM_COMM_STATE_E eRxState;         /* Receive state */
    volatile HALWIRELESSCOMM_COMM_STATE_E eTxState;         /* Transmit state */
    volatile U16 u16RxLength;                               /* Received data length */
    volatile U16 u16TxLength;                               /* Transmitted data length */
    U8 au8RxBuffer[HALWIRELESSCOMM_RX_BUFFER_SIZE_D];       /* Receive buffer */
    U8 au8TxBuffer[HALWIRELESSCOMM_TX_BUFFER_SIZE_D];       /* Transmit buffer */
    BOOL bInitialized;                                      /* Initialization flag */
} HALWIRELESSCOMM_HANDLE_T;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eInit(HALWIRELESSCOMM_HANDLE_T* const psHandle, const HALWIRELESSCOMM_CONFIG_T* const psConfig);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eDeInit(HALWIRELESSCOMM_HANDLE_T* const psHandle);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetDefaultConfig(HALWIRELESSCOMM_CONFIG_T* const psConfig);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eReceive(HALWIRELESSCOMM_HANDLE_T* const psHandle);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eTransmit(HALWIRELESSCOMM_HANDLE_T* const psHandle);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetTxState(const HALWIRELESSCOMM_HANDLE_T* const psHandle, HALWIRELESSCOMM_COMM_STATE_E* const peTxState);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetRxState(const HALWIRELESSCOMM_HANDLE_T* const psHandle, HALWIRELESSCOMM_COMM_STATE_E* const peRxState);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetRxLength(const HALWIRELESSCOMM_HANDLE_T* const psHandle, U16* const pu16Length);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eSetTxLength(HALWIRELESSCOMM_HANDLE_T* const psHandle, const U16 u16Length);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetTxBuffer(const HALWIRELESSCOMM_HANDLE_T* const psHandle, U8** const ppu8TxBuffer);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetRxBuffer(const HALWIRELESSCOMM_HANDLE_T* const psHandle, U8** const ppu8RxBuffer);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetContinuousRx(const HALWIRELESSCOMM_HANDLE_T* const psHandle, BOOL* const pbContinuousRx);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eClearRxComplete(HALWIRELESSCOMM_HANDLE_T* const psHandle);
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eClearTxComplete(HALWIRELESSCOMM_HANDLE_T* const psHandle);
void HalWirelessComm_vRxCompleteCallback(void* const pvUserData);
void HalWirelessComm_vTxCompleteCallback(void* const pvUserData);
void HalWirelessComm_vErrorOccurCallback(void* const pvUserData);

#endif /* HALWIRELESSCOMM_H_ */

//===========================================================================
// End of file.
//===========================================================================








