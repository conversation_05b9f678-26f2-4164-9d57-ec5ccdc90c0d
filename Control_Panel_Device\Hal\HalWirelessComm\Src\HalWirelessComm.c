//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file HalWirelessComm.c
*
* @brief Wireless communication hardware abstraction layer implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "HalWirelessComm.h"
#include "usart.h"
#include <string.h>

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define HALWIRELESSCOMM_DEFAULT_UART_HANDLE_D   (&huart1)

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize the wireless communication hardware.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param psConfig [in]: Configuration parameters pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eInit(HALWIRELESSCOMM_HANDLE_T* const psHandle, const HALWIRELESSCOMM_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (psConfig == NULL_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    /* Check configuration parameter validity */
    if (psConfig->psUartHandle == NULL_D)
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    /* Save configuration parameters */
    psHandle->sConfig = *psConfig;
    
    /* Initialize other members of the handle */
    psHandle->eRxState = HALWIRELESSCOMM_COMM_IDLE_E;
    psHandle->eTxState = HALWIRELESSCOMM_COMM_IDLE_E;
    psHandle->u16RxLength = 0U;
    psHandle->u16TxLength = 0U;
    memset(psHandle->au8RxBuffer, 0U, HALWIRELESSCOMM_RX_BUFFER_SIZE_D);
    memset(psHandle->au8TxBuffer, 0U, HALWIRELESSCOMM_TX_BUFFER_SIZE_D);
    
    /* Enable UART idle interrupt */
    __HAL_UART_ENABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_IDLE);

    /* Enable UART error interrupts */
    __HAL_UART_ENABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_ERR);
    
    psHandle->bInitialized = TRUE_D;
    
    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Deinitialize the wireless communication hardware.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eDeInit(HALWIRELESSCOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if (psHandle == NULL_D) 
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    if (psHandle->bInitialized) 
    {
        /* Stop both receive and transmit DMA operations independently */
        if (HAL_UART_AbortReceive(psHandle->sConfig.psUartHandle) != HAL_OK)
        {
            return HALWIRELESSCOMM_ERROR_E;
        }

        if (HAL_UART_AbortTransmit(psHandle->sConfig.psUartHandle) != HAL_OK)
        {
            return HALWIRELESSCOMM_ERROR_E;
        }
        
        /* Disable UART idle interrupt */
        __HAL_UART_DISABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_IDLE);

        /* Disable UART error interrupts */
        __HAL_UART_DISABLE_IT(psHandle->sConfig.psUartHandle, UART_IT_ERR);
        
        /* Clear handle */
        psHandle->eRxState = HALWIRELESSCOMM_COMM_IDLE_E;
        psHandle->eTxState = HALWIRELESSCOMM_COMM_IDLE_E;
        psHandle->u16RxLength = 0U;
        psHandle->u16TxLength = 0U;
        memset(psHandle->au8RxBuffer, 0U, HALWIRELESSCOMM_RX_BUFFER_SIZE_D);
        memset(psHandle->au8TxBuffer, 0U, HALWIRELESSCOMM_TX_BUFFER_SIZE_D);
        psHandle->bInitialized = FALSE_D;
    }

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Get the default configuration parameters.
* @remark None.
*
* @param psConfig [out]: Configuration structure pointer to return default configuration.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetDefaultConfig(HALWIRELESSCOMM_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    /* Set default configuration */
    psConfig->psUartHandle = HALWIRELESSCOMM_DEFAULT_UART_HANDLE_D;
    psConfig->bContinuousRx = TRUE_D;
    
    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Start receive only.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eReceive(HALWIRELESSCOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }

    /* Check if already receiving */
    if (psHandle->eRxState == HALWIRELESSCOMM_COMM_BUSY_E)
    {
        return HALWIRELESSCOMM_BUSY_E;
    }
    
    /* Clear receive data length */
    psHandle->u16RxLength = 0U;
    
    /* Start DMA reception */
    if (HAL_UARTEx_ReceiveToIdle_DMA(psHandle->sConfig.psUartHandle, psHandle->au8RxBuffer, HALWIRELESSCOMM_RX_BUFFER_SIZE_D) != HAL_OK)
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    /* Update state */
    psHandle->eRxState = HALWIRELESSCOMM_COMM_BUSY_E;

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Start transmit only.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eTransmit(HALWIRELESSCOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (psHandle->u16TxLength == 0U) || (psHandle->u16TxLength > HALWIRELESSCOMM_TX_BUFFER_SIZE_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }

    /* Check if already transmitting */
    if (psHandle->eTxState == HALWIRELESSCOMM_COMM_BUSY_E)
    {
        return HALWIRELESSCOMM_BUSY_E;
    }
    
    /* Start DMA transmission */
    if (HAL_UART_Transmit_DMA(psHandle->sConfig.psUartHandle, psHandle->au8TxBuffer, psHandle->u16TxLength) != HAL_OK)
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    /* Update state */
    psHandle->eTxState = HALWIRELESSCOMM_COMM_BUSY_E;
    
    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Get current transmit state.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param peTxState [out]: Transmit state pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetTxState(const HALWIRELESSCOMM_HANDLE_T* const psHandle, HALWIRELESSCOMM_COMM_STATE_E* const peTxState)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (peTxState == NULL_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    *peTxState = psHandle->eTxState;

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Get current receive state.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param peRxState [out]: Receive state pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetRxState(const HALWIRELESSCOMM_HANDLE_T* const psHandle, HALWIRELESSCOMM_COMM_STATE_E* const peRxState)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (peRxState == NULL_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    *peRxState = psHandle->eRxState;

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Get the length of received data.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param pu16Length [out]: Received data length pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetRxLength(const HALWIRELESSCOMM_HANDLE_T* const psHandle, U16* const pu16Length)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (pu16Length == NULL_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    *pu16Length = psHandle->u16RxLength;

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Set the length of data to transmit.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param u16Length [in]: Data length.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eSetTxLength(HALWIRELESSCOMM_HANDLE_T* const psHandle, const U16 u16Length)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (u16Length > HALWIRELESSCOMM_TX_BUFFER_SIZE_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    psHandle->u16TxLength = u16Length;

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Get the transmit buffer pointer.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param ppu8TxBuffer [out]: Transmit buffer pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetTxBuffer(const HALWIRELESSCOMM_HANDLE_T* const psHandle, U8** const ppu8TxBuffer)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (ppu8TxBuffer == NULL_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    *ppu8TxBuffer = (U8*)psHandle->au8TxBuffer;

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Get the receive buffer pointer.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param ppu8RxBuffer [out]: Receive buffer pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetRxBuffer(const HALWIRELESSCOMM_HANDLE_T* const psHandle, U8** const ppu8RxBuffer)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (ppu8RxBuffer == NULL_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    *ppu8RxBuffer = (U8*)psHandle->au8RxBuffer;

    return HALWIRELESSCOMM_OK_E;
}


/**
* @brief Get the continuous receive mode flag status.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @param pbContinuousRx [out]: Continuous receive mode flag pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eGetContinuousRx(const HALWIRELESSCOMM_HANDLE_T* const psHandle, BOOL* const pbContinuousRx)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (pbContinuousRx == NULL_D))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    *pbContinuousRx = psHandle->sConfig.bContinuousRx;

    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Clear the reception complete flag.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eClearRxComplete(HALWIRELESSCOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    if (psHandle->eRxState == HALWIRELESSCOMM_COMM_COMPLETE_E)
    {
        psHandle->eRxState = HALWIRELESSCOMM_COMM_IDLE_E;
        psHandle->u16RxLength = 0U;
    }
    
    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Clear the transmission complete flag.
* @remark None.
*
* @param psHandle [in]: Hardware handle pointer.
* @return Operation status.
*/
HALWIRELESSCOMM_STATUS_E HalWirelessComm_eClearTxComplete(HALWIRELESSCOMM_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return HALWIRELESSCOMM_ERROR_E;
    }
    
    if (psHandle->eTxState == HALWIRELESSCOMM_COMM_COMPLETE_E)
    {
        psHandle->eTxState = HALWIRELESSCOMM_COMM_IDLE_E;
    }
    
    return HALWIRELESSCOMM_OK_E;
}

/**
* @brief Receive complete interrupt callback function (to be called in receive complete interrupt service routine).
* @remark None.
*
* @param pvUserData [in]: User data pointer (should be HALWIRELESSCOMM_HANDLE_T pointer).
* @return Nothing.
*/
void HalWirelessComm_vRxCompleteCallback(void* const pvUserData)
{
    /* Parameter validity check */
    if (pvUserData == NULL_D)
    {
        return;
    }

    /* Convert user data to handle pointer */
    HALWIRELESSCOMM_HANDLE_T* const psHandle = (HALWIRELESSCOMM_HANDLE_T*)pvUserData;
    
    if ((psHandle->bInitialized) && (psHandle->eRxState == HALWIRELESSCOMM_COMM_BUSY_E))
    {
        /* Get received data length */
        U16 u16RxLength = HALWIRELESSCOMM_RX_BUFFER_SIZE_D - __HAL_DMA_GET_COUNTER(psHandle->sConfig.psUartHandle->hdmarx);

        if (u16RxLength > 0U)
        {
            /* Set received data length and complete flag */
            psHandle->u16RxLength = u16RxLength;
            psHandle->eRxState = HALWIRELESSCOMM_COMM_COMPLETE_E;
        }
    }
}

/**
* @brief Transmit complete callback function (to be called in transmit complete interrupt service routine).
* @remark None.
*
* @param pvUserData [in]: User data pointer (should be HALWIRELESSCOMM_HANDLE_T pointer).
* @return Nothing.
*/
void HalWirelessComm_vTxCompleteCallback(void* const pvUserData)
{
    /* Parameter validity check */
    if (pvUserData == NULL_D)
    {
        return;
    }

    /* Convert user data to handle pointer */
    HALWIRELESSCOMM_HANDLE_T* const psHandle = (HALWIRELESSCOMM_HANDLE_T*)pvUserData;
    
    if ((psHandle->bInitialized) && (psHandle->eTxState == HALWIRELESSCOMM_COMM_BUSY_E))
    {
        /* Set transmission complete flag */
        psHandle->eTxState = HALWIRELESSCOMM_COMM_COMPLETE_E;
    }
}

/**
* @brief Error callback function (to be called in error interrupt service routine).
* @remark None.
*
* @param pvUserData [in]: User data pointer (should be HALWIRELESSCOMM_HANDLE_T pointer).
* @return Nothing.
*/
void HalWirelessComm_vErrorOccurCallback(void* const pvUserData)
{
    /* Parameter validity check */
    if (pvUserData == NULL_D)
    {
        return;
    }

    /* Convert user data to handle pointer */
    HALWIRELESSCOMM_HANDLE_T* const psHandle = (HALWIRELESSCOMM_HANDLE_T*)pvUserData;
    
    /* Initialized status check */
    if (psHandle->bInitialized)
    {
        /* Set error state */
        psHandle->eRxState = HALWIRELESSCOMM_COMM_ERROR_E;
        psHandle->eTxState = HALWIRELESSCOMM_COMM_ERROR_E;
    }
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

//===========================================================================
// End of file.
//===========================================================================








