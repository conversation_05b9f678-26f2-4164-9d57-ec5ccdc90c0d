<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>4</zoom_level>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>260</x>
      <y>24</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>236</x>
      <y>44</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>256</x>
      <y>76</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>348</x>
      <y>80</y>
      <w>12</w>
      <h>304</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;740.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>232</x>
      <y>104</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check input pins
Validate all 4 pins</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>88</y>
      <w>48</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointers]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>256</x>
      <y>136</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>120</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>232</x>
      <y>164</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check output pins
Validate all 4 pins</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>148</y>
      <w>52</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid input pins]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>256</x>
      <y>196</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>180</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>268</x>
      <y>196</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid output pins]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>256</x>
      <y>256</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>268</x>
      <y>256</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid delay]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>232</x>
      <y>284</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Save Config
Set initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>268</y>
      <w>40</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid delay]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>232</x>
      <y>316</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Initialize output pins
Set all to high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>300</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>256</x>
      <y>348</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>332</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>268</x>
      <y>348</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Init failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>232</x>
      <y>376</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>360</y>
      <w>44</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Init success]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>260</x>
      <y>424</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>388</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>496</x>
      <y>24</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>472</x>
      <y>44</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>28</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>492</x>
      <y>76</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>60</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>560</x>
      <y>268</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>588</x>
      <y>80</y>
      <w>12</w>
      <h>196</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;470.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>492</x>
      <y>136</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>120</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>468</x>
      <y>164</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set all output high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>148</y>
      <w>36</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>492</x>
      <y>196</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>180</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>196</y>
      <w>96</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Set failed]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>468</x>
      <y>268</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>208</y>
      <w>44</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Set success]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>240</y>
      <w>12</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>736</x>
      <y>24</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>712</x>
      <y>44</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>28</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>732</x>
      <y>76</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>60</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>796</x>
      <y>216</y>
      <w>60</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>824</x>
      <y>80</y>
      <w>12</w>
      <h>144</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;340.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>708</x>
      <y>108</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set input pins config
All 4 input pins</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>88</y>
      <w>44</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>708</x>
      <y>144</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set output pins config
All 4 output pins</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>124</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>704</x>
      <y>180</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set default scan delay</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>160</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>708</x>
      <y>216</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>196</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>308</x>
      <y>496</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>284</x>
      <y>516</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>500</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>304</x>
      <y>548</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>532</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>284</x>
      <y>580</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Clear Key Matrix
Initialize to 0</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>560</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>596</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>272</x>
      <y>652</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set column lines
Current low, others high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>304</x>
      <y>688</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>668</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>688</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Set failed]</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>720</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Delay for stable</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>700</y>
      <w>44</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Set success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>736</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>792</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Read input line</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>304</x>
      <y>828</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>808</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>828</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Read failed]</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>304</x>
      <y>896</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>876</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>928</y>
      <w>60</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set key bit
Update matrix</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>908</y>
      <w>44</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Key pressed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>316</x>
      <y>896</y>
      <w>84</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Key not pressed]</panel_attributes>
    <additional_attributes>190.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>760</y>
      <w>116</w>
      <h>208</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next row]</panel_attributes>
    <additional_attributes>90.0;10.0;210.0;10.0;210.0;500.0;10.0;500.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>984</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Restore all lines high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>944</y>
      <w>12</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>304</x>
      <y>1020</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>1000</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>1020</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Restore failed]</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>1052</y>
      <w>60</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Save last matrix</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>1032</y>
      <w>56</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Restore success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>556</x>
      <y>496</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>532</x>
      <y>516</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>500</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>552</x>
      <y>548</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>532</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>604</x>
      <y>612</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>632</x>
      <y>552</y>
      <w>12</w>
      <h>68</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>528</x>
      <y>580</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Set GPIO pin</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>560</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>528</x>
      <y>612</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>592</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>188</x>
      <y>0</y>
      <w>224</w>
      <h>444</h>
    </coordinates>
    <panel_attributes>HalKey Init
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>28</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>60</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>320</x>
      <y>376</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>772</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>756</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Row Loop
row = 0 to 3</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>632</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>620</y>
      <w>168</w>
      <h>360</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next column]</panel_attributes>
    <additional_attributes>90.0;10.0;320.0;10.0;320.0;880.0;10.0;880.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>616</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Column Loop
col = 0 to 3</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>556</x>
      <y>660</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>624</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>280</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>496</x>
      <y>316</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>388</y>
      <w>100</w>
      <h>28</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>496</x>
      <y>472</y>
      <w>196</w>
      <h>340</h>
    </coordinates>
    <panel_attributes>HalKey SetOutputLine</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>232</x>
      <y>224</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check scan delay time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>208</y>
      <w>56</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid output pins]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>260</x>
      <y>240</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>468</x>
      <y>104</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check initialized state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>88</y>
      <w>44</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>268</x>
      <y>136</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid input pins]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>268</x>
      <y>76</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Null pointers]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>504</x>
      <y>76</y>
      <w>96</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>440</x>
      <y>136</y>
      <w>60</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;130.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>468</x>
      <y>224</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Clear handle data</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>440</x>
      <y>140</y>
      <w>68</w>
      <h>124</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>150.0;290.0;10.0;290.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>496</x>
      <y>280</y>
      <w>104</w>
      <h>28</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;240.0;50.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>76</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>736</x>
      <y>264</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>228</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>228</y>
      <w>100</w>
      <h>28</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>668</x>
      <y>0</y>
      <w>224</w>
      <h>340</h>
    </coordinates>
    <panel_attributes>HalKey GetDefaultConfig</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>192</x>
      <y>1088</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>548</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>860</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check key press</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>840</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Read success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>280</x>
      <y>1088</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>552</y>
      <w>12</w>
      <h>544</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;1340.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>308</x>
      <y>1136</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>1100</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>1100</y>
      <w>100</w>
      <h>28</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>230.0;50.0;10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>308</x>
      <y>1068</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>184</x>
      <y>472</y>
      <w>296</w>
      <h>692</h>
    </coordinates>
    <panel_attributes>HalKey ScanMatrix</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>564</x>
      <y>548</y>
      <w>80</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>624</y>
      <w>88</w>
      <h>28</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;200.0;50.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>428</x>
      <y>0</y>
      <w>224</w>
      <h>340</h>
    </coordinates>
    <panel_attributes>HalKey DeInit</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>816</x>
      <y>612</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>740</x>
      <y>612</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>624</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>624</y>
      <w>88</w>
      <h>28</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;200.0;50.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>768</x>
      <y>660</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>844</x>
      <y>552</y>
      <w>12</w>
      <h>68</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>592</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>740</x>
      <y>580</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Read GPIO pin</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>708</x>
      <y>472</y>
      <w>196</w>
      <h>340</h>
    </coordinates>
    <panel_attributes>HalKey ReadInputLine</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>560</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>764</x>
      <y>548</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>776</x>
      <y>548</y>
      <w>80</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>532</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>744</x>
      <y>516</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>768</x>
      <y>500</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>768</x>
      <y>496</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>496</x>
      <y>824</y>
      <w>196</w>
      <h>340</h>
    </coordinates>
    <panel_attributes>HalKey GetLastKeyMatrix</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>852</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>556</x>
      <y>848</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>532</x>
      <y>868</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>884</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>912</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>564</x>
      <y>900</y>
      <w>80</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>552</x>
      <y>900</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>632</x>
      <y>904</y>
      <w>12</w>
      <h>68</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;150.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>604</x>
      <y>964</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>976</y>
      <w>88</w>
      <h>28</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;200.0;50.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>976</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>556</x>
      <y>1012</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>528</x>
      <y>964</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return 
HALKEY_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>556</x>
      <y>944</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>528</x>
      <y>932</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Get Last Matrix
Set saved value</panel_attributes>
    <additional_attributes/>
  </element>
</diagram>
