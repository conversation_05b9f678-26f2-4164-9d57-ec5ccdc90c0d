/**
 * @remark 版权所有(C)2025 TRIED.
 * @remark 本软件受版权法保护，未经授权禁止复制、分发或修改。
 * @file CircularBuffer.c
 * @brief 环形缓冲区实现文件
 * @details 实现单向环形缓冲区的功能，包括数据读写、状态查询等
 * <AUTHOR>
 * @date 2025-06-13
 * @note 适用于STM32F4系列微控制器
 */

/* Includes ------------------------------------------------------------------*/
#include "CircularBuffer.h"

/* Private define ------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/

/* Private functions ---------------------------------------------------------*/

/* Exported functions --------------------------------------------------------*/
/**
 * @brief 初始化单向循环缓冲区
 * @param psBuffer 缓冲区结构体指针
 * @param pu8BufferMem 缓冲区内存指针
 * @param u16Size 缓冲区大小
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eInit(CIRCULARBUFFER_T* psBuffer, uint8_t* pu8BufferMem, uint16_t u16Size)
{
    if (psBuffer == NULL || pu8BufferMem == NULL || u16Size == 0 || u16Size > CIRCULARBUFFER_MAX_SIZE) {
        return CIRCULARBUFFER_INVALID_PARAM;
    }
    
    psBuffer->pu8Buffer = pu8BufferMem;
    psBuffer->u16Size = u16Size;
    psBuffer->u16Head = 0;
    psBuffer->u16Tail = 0;
    psBuffer->u16Count = 0;
    psBuffer->bInitialized = true;
    
    return CIRCULARBUFFER_OK;
}

/**
 * @brief 反初始化单向循环缓冲区
 * @param psBuffer 缓冲区结构体指针
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eDeInit(CIRCULARBUFFER_T* psBuffer)
{
    if (psBuffer == NULL) {
        return CIRCULARBUFFER_INVALID_PARAM;
    }
    
    psBuffer->pu8Buffer = NULL;
    psBuffer->u16Size = 0;
    psBuffer->u16Head = 0;
    psBuffer->u16Tail = 0;
    psBuffer->u16Count = 0;
    psBuffer->bInitialized = false;
    
    return CIRCULARBUFFER_OK;
}

/**
 * @brief 向循环缓冲区写入单个字节
 * @param psBuffer 缓冲区结构体指针
 * @param u8Data 要写入的数据
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_ePutByte(CIRCULARBUFFER_T* psBuffer, uint8_t u8Data)
{
    if (psBuffer == NULL || !psBuffer->bInitialized) {
        return CIRCULARBUFFER_INVALID_PARAM;
    }
    
    if (psBuffer->u16Count >= psBuffer->u16Size) {
        return CIRCULARBUFFER_FULL;
    }
    
    psBuffer->pu8Buffer[psBuffer->u16Head] = u8Data;
    psBuffer->u16Head = (psBuffer->u16Head + 1) % psBuffer->u16Size;
    psBuffer->u16Count++;
    
    return CIRCULARBUFFER_OK;
}

/**
 * @brief 从循环缓冲区读取单个字节
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 读取数据的指针
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eGetByte(CIRCULARBUFFER_T* psBuffer, uint8_t* pu8Data)
{
    if (psBuffer == NULL || pu8Data == NULL || !psBuffer->bInitialized) {
        return CIRCULARBUFFER_INVALID_PARAM;
    }
    
    if (psBuffer->u16Count == 0) {
        return CIRCULARBUFFER_EMPTY;
    }
    
    *pu8Data = psBuffer->pu8Buffer[psBuffer->u16Tail];
    psBuffer->u16Tail = (psBuffer->u16Tail + 1) % psBuffer->u16Size;
    psBuffer->u16Count--;
    
    return CIRCULARBUFFER_OK;
}

/**
 * @brief 向循环缓冲区写入多个字节
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 要写入的数据指针
 * @param u16Length 数据长度
 * @retval uint16_t 实际写入的字节数
 */
uint16_t CircularBuffer_u16PutBytes(CIRCULARBUFFER_T* psBuffer, const uint8_t* pu8Data, uint16_t u16Length)
{
    if (psBuffer == NULL || pu8Data == NULL || !psBuffer->bInitialized || u16Length == 0) {
        return 0;
    }
    
    uint16_t u16Written = 0;
    uint16_t u16FreeSpace = psBuffer->u16Size - psBuffer->u16Count;
    uint16_t u16ToWrite = (u16Length <= u16FreeSpace) ? u16Length : u16FreeSpace;
    
    for (uint16_t i = 0; i < u16ToWrite; i++) {
        psBuffer->pu8Buffer[psBuffer->u16Head] = pu8Data[i];
        psBuffer->u16Head = (psBuffer->u16Head + 1) % psBuffer->u16Size;
        u16Written++;
    }
    
    psBuffer->u16Count += u16Written;
    
    return u16Written;
}

/**
 * @brief 从循环缓冲区读取多个字节
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 读取数据的指针
 * @param u16Length 要读取的数据长度
 * @retval uint16_t 实际读取的字节数
 */
uint16_t CircularBuffer_u16GetBytes(CIRCULARBUFFER_T* psBuffer, uint8_t* pu8Data, uint16_t u16Length)
{
    if (psBuffer == NULL || pu8Data == NULL || !psBuffer->bInitialized || u16Length == 0) {
        return 0;
    }
    
    uint16_t u16Read = 0;
    uint16_t u16ToRead = (u16Length <= psBuffer->u16Count) ? u16Length : psBuffer->u16Count;
    
    for (uint16_t i = 0; i < u16ToRead; i++) {
        pu8Data[i] = psBuffer->pu8Buffer[psBuffer->u16Tail];
        psBuffer->u16Tail = (psBuffer->u16Tail + 1) % psBuffer->u16Size;
        u16Read++;
    }
    
    psBuffer->u16Count -= u16Read;
    
    return u16Read;
}

/**
 * @brief 获取循环缓冲区中可用数据的长度
 * @param psBuffer 缓冲区结构体指针
 * @retval uint16_t 可用数据长度
 */
uint16_t CircularBuffer_u16GetCount(const CIRCULARBUFFER_T* psBuffer)
{
    if (psBuffer == NULL || !psBuffer->bInitialized) {
        return 0;
    }
    
    return psBuffer->u16Count;
}

/**
 * @brief 获取循环缓冲区中剩余空间大小
 * @param psBuffer 缓冲区结构体指针
 * @retval uint16_t 剩余空间大小
 */
uint16_t CircularBuffer_u16GetFreeSpace(const CIRCULARBUFFER_T* psBuffer)
{
    if (psBuffer == NULL || !psBuffer->bInitialized) {
        return 0;
    }
    
    return psBuffer->u16Size - psBuffer->u16Count;
}

/**
 * @brief 检查循环缓冲区是否为空
 * @param psBuffer 缓冲区结构体指针
 * @retval bool true-空，false-非空
 */
bool CircularBuffer_bIsEmpty(const CIRCULARBUFFER_T* psBuffer)
{
    if (psBuffer == NULL || !psBuffer->bInitialized) {
        return true;
    }
    
    return (psBuffer->u16Count == 0);
}

/**
 * @brief 检查循环缓冲区是否已满
 * @param psBuffer 缓冲区结构体指针
 * @retval bool true-满，false-未满
 */
bool CircularBuffer_bIsFull(const CIRCULARBUFFER_T* psBuffer)
{
    if (psBuffer == NULL || !psBuffer->bInitialized) {
        return false;
    }
    
    return (psBuffer->u16Count >= psBuffer->u16Size);
}

/**
 * @brief 清空循环缓冲区
 * @param psBuffer 缓冲区结构体指针
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eClear(CIRCULARBUFFER_T* psBuffer)
{
    if (psBuffer == NULL || !psBuffer->bInitialized) {
        return CIRCULARBUFFER_INVALID_PARAM;
    }
    
    psBuffer->u16Head = 0;
    psBuffer->u16Tail = 0;
    psBuffer->u16Count = 0;
    
    return CIRCULARBUFFER_OK;
}

/**
 * @brief 预览循环缓冲区数据（不从缓冲区中移除）
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 预览数据的指针
 * @param u16Length 要预览的数据长度
 * @retval uint16_t 实际预览的字节数
 */
uint16_t CircularBuffer_u16Peek(const CIRCULARBUFFER_T* psBuffer, uint8_t* pu8Data, uint16_t u16Length)
{
    if (psBuffer == NULL || pu8Data == NULL || !psBuffer->bInitialized || u16Length == 0) {
        return 0;
    }
    
    uint16_t u16Read = 0;
    uint16_t u16ToRead = (u16Length <= psBuffer->u16Count) ? u16Length : psBuffer->u16Count;
    uint16_t u16TempTail = psBuffer->u16Tail;
    
    for (uint16_t i = 0; i < u16ToRead; i++) {
        pu8Data[i] = psBuffer->pu8Buffer[u16TempTail];
        u16TempTail = (u16TempTail + 1) % psBuffer->u16Size;
        u16Read++;
    }
    
    return u16Read;
} 