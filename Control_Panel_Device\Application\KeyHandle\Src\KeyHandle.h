//----------------------------------------------------------------------------
/**
* @file KeyHandle.h
* @remark KeyHandle public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef KEYHANDLE_H_
#define KEYHANDLE_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "HalKey.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------

#define KEYHANDLE_MAX_COMBO_SIZE_D                4U       /* Maximum number of keys contained in a single combination key */


/**
 * @brief Key handling module status enumeration
 */
typedef enum
{
    KEYHANDLE_OK_E = 0U,                        /* Operation successful */
    KEYHANDLE_ERROR_E                           /* Operation failed/error */
} KEYHANDLE_STATUS_E;

/**
 * @brief Key event type enumeration
 */
typedef enum
{
    KEYHANDLE_EVENT_SINGLE_SHORT_PRESS_E = 0U,         /* Short press event (quick release after press) */
    KEYHANDLE_EVENT_SINGLE_LONG_PRESS_E,               /* Long press event (press exceeds specified time) */
    KEYHANDLE_EVENT_COMBO_SHORT_PRESS_E         /* Combination key short press event */
} KEYHANDLE_EVENT_TYPE_E;

/**
 * @brief Key handling state machine state enumeration
 */
typedef enum
{
    KEYHANDLE_STATE_SCAN_E = 0,                     /* Scanning key matrix state */
    KEYHANDLE_STATE_DEBOUNCE_E,                 /* Key debounce processing state */
    KEYHANDLE_STATE_DETECT_SINGLE_E,            /* Single key event detection state */
    KEYHANDLE_STATE_DETECT_COMBO_E,             /* Combination key event detection state */
} KEYHANDLE_STATE_MACHINE_E;

/**
 * @brief Single key type enumeration
 */
typedef enum
{
    KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E = 0U,      /* Left function key */
    KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E,          /* Right function key */
    KEYHANDLE_SINGLE_KEY_UP_E,                  /* Up direction key */
    KEYHANDLE_SINGLE_KEY_DOWN_E,                /* Down direction key */
    KEYHANDLE_SINGLE_KEY_LEFT_E,                /* Left direction key */
    KEYHANDLE_SINGLE_KEY_RIGHT_E,               /* Right direction key */
    KEYHANDLE_SINGLE_KEY_START_E,               /* Start key */
    KEYHANDLE_SINGLE_KEY_STOP_E,                /* Stop key */
    KEYHANDLE_SINGLE_KEY_HELP_E,                /* Help key */
    KEYHANDLE_SINGLE_KEY_LOCAL_REMOTE_E,        /* Local/Remote toggle key */
    KEYHANDLE_SINGLE_KEY_MAX_E
} KEYHANDLE_SINGLE_KEY_E;

/**
 * @brief Combination key type enumeration
 */
typedef enum
{
    KEYHANDLE_COMBO_KEY_LEFT_RIGHT_E = 0U,          /* Left direction key + Right direction key */
    KEYHANDLE_COMBO_KEY_LEFT_UP_E,                  /* Left direction key + Up direction key */
    KEYHANDLE_COMBO_KEY_LEFT_DOWN_E,                /* Left direction key + Down direction key */
    KEYHANDLE_COMBO_KEY_RIGHT_UP_E,                 /* Right direction key + Up direction key */
    KEYHANDLE_COMBO_KEY_RIGHT_DOWN_E,               /* Right direction key + Down direction key */
    KEYHANDLE_COMBO_KEY_LEFT_HELP_E,                /* Left direction key + Help key */
    KEYHANDLE_COMBO_KEY_MAX_E
} KEYHANDLE_COMBO_KEY_E;

/**
 * @brief Single key mapping structure
 */
typedef struct
{
    KEYHANDLE_SINGLE_KEY_E eSingleKey;                          /* Single key type (logical key) */
    U8 u8MatrixKey;                                             /* Corresponding matrix key index (physical key position, 0-15) */
} KEYHANDLE_SINGLE_KEY_MAP_T;

/**
 * @brief Combination key mapping structure
 */
typedef struct
{
    KEYHANDLE_COMBO_KEY_E eComboKey;                            /* Combination key type */
    U8 u8KeyCount;                                              /* Number of keys contained in the combination */
    KEYHANDLE_SINGLE_KEY_E aeKeys[KEYHANDLE_MAX_COMBO_SIZE_D];  /* List of keys in the combination */
} KEYHANDLE_COMBO_KEY_MAP_T;

/**
 * @brief Key event structure
 */
typedef struct
{
    KEYHANDLE_EVENT_TYPE_E eEventType;                          /* Event type */
    KEYHANDLE_SINGLE_KEY_MAP_T sKeyEventData;                   /* Single key event data */
    KEYHANDLE_COMBO_KEY_MAP_T sComboEventData;                  /* Combination key event data */
    U32 u32Timestamp;                                           /* Timestamp when the event occurred (milliseconds) */
} KEYHANDLE_EVENT_T;

/**
 * @brief Key handling configuration structure
 */
typedef struct
{
    U32 u32SingleKeyDebounceTimeMs;                                                         /* Key debounce time (milliseconds) */
    U32 u32SingleKeyLongPressTimeMs;                                                        /* Long press detection time (milliseconds) */
    U32 u32SingleKeyLongPressRepeatMs;                                                      /* Long press repeat trigger period (milliseconds) */
    KEYHANDLE_SINGLE_KEY_MAP_T asSingleKeyMaps[KEYHANDLE_SINGLE_KEY_MAX_E];                 /* Single key mapping configuration array */
    KEYHANDLE_COMBO_KEY_MAP_T asComboKeyMaps[KEYHANDLE_COMBO_KEY_MAX_E];                    /* Combination key mapping array */
    void (*apvSingleKeyShortPressCallbacks[KEYHANDLE_SINGLE_KEY_MAX_E])(const KEYHANDLE_EVENT_T* const psEvent);   /* Single key short press callback functions array */
    void (*apvSingleKeyLongPressCallbacks[KEYHANDLE_SINGLE_KEY_MAX_E])(const KEYHANDLE_EVENT_T* const psEvent);    /* Single key long press callback functions array */
    void (*apvComboKeyShortPressCallbacks[KEYHANDLE_COMBO_KEY_MAX_E])(const KEYHANDLE_EVENT_T* const psEvent);     /* Combination key short press callback functions array */
} KEYHANDLE_CONFIG_T;

/**
 * @brief Key state structure
 */
typedef struct
{
    KEYHANDLE_STATE_MACHINE_E eCurrentStateMachine;                                /* State machine current state */
    U16 u16CurrentKeyMatrix;                                                   /* Current key matrix state (16-bit bitmap) */
    U16 u16PreviousKeyMatrix;                                                  /* Previous key matrix state (for edge detection) */
    U16 u16DebouncedKeyMatrix;                                                 /* Debounced key matrix state */
    U32 au32SingleKeyPressTime[KEYHANDLE_SINGLE_KEY_MAX_E];                          /* Press timestamp array for each key */
    U32 au32SingleKeyDebounceTime[KEYHANDLE_SINGLE_KEY_MAX_E];                       /* Debounce start time array for each key */
    U32 au32SingleKeyLastLongPressTime[KEYHANDLE_SINGLE_KEY_MAX_E];                  /* Last long press trigger time array for each key */
    BOOL abSingleKeyLongPressProcessed[KEYHANDLE_SINGLE_KEY_MAX_E];                           /* Processed flag array for each key */
    U8 u8ComboKeyActiveKeyCount;                                                  /* Number of keys in the currently active combination */
    KEYHANDLE_SINGLE_KEY_E aeComboKeyActiveKeys[KEYHANDLE_MAX_COMBO_SIZE_D];       /* Currently active combination key list */
    BOOL bInComboMode;                                                      /* Flag indicating if currently processing combination keys */
    U8 u8CurrentKeyCount;                                                   /* Current number of pressed keys */
} KEYHANDLE_STATE_T;

/**
 * @brief Key handling handle structure
 */
typedef struct
{
    KEYHANDLE_CONFIG_T sConfig;             /* Configuration parameter structure */
    KEYHANDLE_STATE_T sState;               /* State information structure */
    HALKEY_HANDLE_T sHalKey;                /* Hardware abstraction layer handle */
    BOOL bInitialized;                      /* Module initialization flag */
} KEYHANDLE_HANDLE_T;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
KEYHANDLE_STATUS_E KeyHandle_eInit(KEYHANDLE_HANDLE_T* const psHandle);
KEYHANDLE_STATUS_E KeyHandle_eProcess(KEYHANDLE_HANDLE_T* const psHandle);
KEYHANDLE_STATUS_E KeyHandle_eSetSingleKeyShortPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, void (*pvCallback)(const KEYHANDLE_EVENT_T* const psEvent));
KEYHANDLE_STATUS_E KeyHandle_eSetSingleKeyLongPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, void (*pvCallback)(const KEYHANDLE_EVENT_T* const psEvent));
KEYHANDLE_STATUS_E KeyHandle_eSetComboKeyShortPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_COMBO_KEY_E eComboKey, void (*pvCallback)(const KEYHANDLE_EVENT_T* const psEvent));

#endif /* KEYHANDLE_H_ */

//===========================================================================
// End of file.
//===========================================================================








