- [HalWireComm](#HalWireComm)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# HalWireComm

## 描述

`HalWireComm`作为RS485通信的硬件抽象层。该模块主要负责RS485通信的硬件操作，包括UART和DMA的初始化与控制、方向控制、数据收发算法的实现，为上层应用提供稳定可靠的RS485通信功能。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0044    |   控制盘组网切换   |

### 软件需求

1) 应能够实现用于内部通信的RS485发送和接收功能的抽象

### 假设

1) 硬件上连接的是标准的RS485收发器。
2) GPIO引脚已在`main.h`中正确定义并完成时钟配置。
3) UART外设已正确配置并支持DMA传输。
4) RS485方向控制引脚配置为推挽输出模式。
5) DMA通道已正确配置用于UART的收发。

***

## 平台资源

 接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HAL_GPIO_WritePin()` | 设置RS485方向控制引脚的输出电平（高/低）。 |
| `HAL_UARTEx_ReceiveToIdle_DMA()` | 启动UART DMA接收到空闲中断。 |
| `HAL_UART_Transmit_DMA()` | 启动UART DMA发送。 |
| `HAL_UART_DMAStop()` | 停止UART DMA传输。 |
| `__HAL_UART_ENABLE_IT()` | 使能UART中断。 |
| `__HAL_UART_DISABLE_IT()` | 禁用UART中断。 |
| `__HAL_DMA_GET_COUNTER()` | 获取DMA传输剩余计数。 |
| `Utility_vDelayUs()` | 微秒级延时。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用RS485通信协议，结合DMA技术实现高效的数据传输。该方法通过方向控制引脚切换收发模式，使用DMA进行数据传输以减少CPU占用，并提供完整的回调机制处理通信事件。具体特点如下：

1. 支持RS485收发方向自动控制。
2. 使用DMA进行数据传输，提高传输效率。
3. 支持空闲中断检测，实现变长数据接收。
4. 提供完整的回调机制处理收发和错误事件。
5. 支持通信状态监控和错误处理。
6. 回调函数采用通用签名`void (*pvCallback)(void *pvUserData)`，便于与统一的UART回调管理器集成。

通过这种方式，可以实现高效稳定的RS485通信功能。

### 静态设计

模块的静态设计如下所示：

![类图](Image/HalWireComm_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/HalWireComm_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：
   
   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `HalWireComm_eInit()` | 3      |
   | `HalWireComm_eDeInit()` | 3 |
   | `HalWireComm_eGetDefaultConfig()` | 2 |
   | `HalWireComm_eSetDirection()` | 2 |
   | `HalWireComm_eReceive()` | 5 |
   | `HalWireComm_eTransmit()` | 5 |
   | `HalWireComm_eStopTransfer()` | 3 |
   | `HalWireComm_eGetState()` | 2 |
   | `HalWireComm_eIsTxComplete()` | 2 |
   | `HalWireComm_eIsRxComplete()` | 2 |
   | `HalWireComm_eGetRxLength()` | 2 |
   | `HalWireComm_eSetTxLength()` | 2 |
   | `HalWireComm_eClearRxComplete()` | 2 |
   | `HalWireComm_eClearTxComplete()` | 2 |
   | `HalWireComm_eGetRxBuffer()` | 2 |
   | `HalWireComm_eGetTxBuffer()` | 2 |
   | `HalWireComm_vRxCompleteCallback()` | 5 |
   | `HalWireComm_vTxCompleteCallback()` | 4 |
   | `HalWireComm_vErrorOccurCallback()` | 3 |
   
2. 其他测试项：目前无免费工具，暂不列出。  

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用STM32CubeIDE搭建测试工程，硬件板卡为安富莱STM32-V5开发板。

   1.2 使用FreeRTOS新建StartWireCommTask任务，在其中实现HalWireComm模块的RS485通信测试方案。任务调度时间为10ms。

2. 函数测试详细结果

   2.1 HalWireComm_eInit()

      2.1.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.2 分支2：空指针检测（配置）

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.3 分支3：UART句柄空指针检测

      - 测试用例：`psConfig->psUartHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测到无效配置

      2.1.4 分支4：RS485方向控制端口空指针检测

      - 测试用例：`psConfig->psRs485DirPort = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测到无效配置

      2.1.5 分支5：RS485方向控制引脚检测

      - 测试用例：`psConfig->u16Rs485DirPin = 0U`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测到无效引脚配置

      2.1.6 分支6：正常初始化

      - 测试用例：所有参数有效，配置正确
      - 预期结果：返回 `HALWIRECOMM_OK_E`，句柄状态正确设置
      - 测试结果：通过，初始化成功，UART中断使能，缓冲区清零

   2.2 HalWireComm_eDeInit()

      2.2.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.2.2 分支2：未初始化状态

      - 测试用例：未初始化状态，`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_OK_E`，直接成功
      - 测试结果：通过，函数跳过反初始化操作

      2.2.3 分支3：正常反初始化

      - 测试用例：已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，句柄状态清零
      - 测试结果：通过，反初始化成功，DMA停止，中断禁用，缓冲区清零

   2.3 HalWireComm_eGetDefaultConfig()

      2.3.1 分支1：空指针检测

      - 测试用例：传入 `psConfig = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.3.2 分支2：正常获取默认配置

      - 测试用例：传入有效配置指针
      - 预期结果：返回 `HALWIRECOMM_OK_E`，配置参数正确设置
      - 测试结果：通过，默认配置参数正确设置为默认UART句柄和RS485控制引脚

   2.4 HalWireComm_eSetDirection()

      2.4.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.4.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.4.3 分支3：设置发送方向

      - 测试用例：`eDirection = HALWIRECOMM_DIR_TX_E`
      - 预期结果：返回 `HALWIRECOMM_OK_E`，方向控制引脚设置为高电平
      - 测试结果：通过，RS485切换到发送模式，延时正确执行

      2.4.4 分支4：设置接收方向

      - 测试用例：`eDirection = HALWIRECOMM_DIR_RX_E`
      - 预期结果：返回 `HALWIRECOMM_OK_E`，方向控制引脚设置为低电平
      - 测试结果：通过，RS485切换到接收模式，延时正确执行

   2.5 HalWireComm_eReceive()

      2.5.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.5.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.5.3 分支3：DMA停止失败

      - 测试用例：DMA停止操作失败
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确处理DMA停止失败

      2.5.4 分支4：方向设置失败

      - 测试用例：设置接收方向失败
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确处理方向设置失败

      2.5.5 分支5：DMA接收启动失败

      - 测试用例：HAL_UARTEx_ReceiveToIdle_DMA 返回错误
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确处理DMA接收启动失败

      2.5.6 分支6：正常启动接收

      - 测试用例：所有操作正常
      - 预期结果：返回 `HALWIRECOMM_OK_E`，状态设置为接收模式
      - 测试结果：通过，DMA接收启动成功，状态正确切换

   2.6 HalWireComm_eTransmit()

      2.6.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.6.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.6.3 分支3：发送长度为零检测

      - 测试用例：`u16TxLength = 0U`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测无效发送长度

      2.6.4 分支4：发送长度超出缓冲区检测

      - 测试用例：`u16TxLength > HALWIRECOMM_TX_BUFFER_SIZE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测发送长度超限

      2.6.5 分支5：DMA停止失败

      - 测试用例：DMA停止操作失败
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确处理DMA停止失败

      2.6.6 分支6：方向设置失败

      - 测试用例：设置发送方向失败
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确处理方向设置失败

      2.6.7 分支7：DMA发送启动失败

      - 测试用例：HAL_UART_Transmit_DMA 返回错误
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确处理DMA发送启动失败

      2.6.8 分支8：正常启动发送

      - 测试用例：所有操作正常，有效发送长度
      - 预期结果：返回 `HALWIRECOMM_OK_E`，状态设置为发送模式
      - 测试结果：通过，DMA发送启动成功，状态正确切换

   2.7 HalWireComm_eStopTransfer()

      2.7.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.7.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.7.3 分支3：DMA停止失败

      - 测试用例：HAL_UART_DMAStop 返回错误
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确处理DMA停止失败

      2.7.4 分支4：正常停止DMA

      - 测试用例：所有操作正常
      - 预期结果：返回 `HALWIRECOMM_OK_E`，状态设置为空闲，标志清零
      - 测试结果：通过，DMA停止成功，状态和标志正确清零

   2.8 HalWireComm_eGetState()

      2.8.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.8.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.8.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `peState = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.8.4 分支4：正常获取状态

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`*peState = eState`
      - 测试结果：通过，正确返回当前通信状态

   2.9 HalWireComm_eIsTxComplete()

      2.9.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.9.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.9.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `pbComplete = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.9.4 分支4：正常获取发送完成状态

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`*pbComplete = bTxComplete`
      - 测试结果：通过，正确返回发送完成状态

   2.10 HalWireComm_eIsRxComplete()

      2.10.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.10.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.10.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `pbComplete = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.10.4 分支4：正常获取接收完成状态

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`*pbComplete = bRxComplete`
      - 测试结果：通过，正确返回接收完成状态

   2.11 HalWireComm_eGetRxLength()

      2.11.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.11.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.11.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `pu16Length = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.11.4 分支4：正常获取接收长度

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`*pu16Length = u16RxLength`
      - 测试结果：通过，正确返回接收数据长度

   2.12 HalWireComm_eSetTxLength()

      2.12.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.12.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.12.3 分支3：发送长度超出缓冲区检测

      - 测试用例：`u16Length > HALWIRECOMM_TX_BUFFER_SIZE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测长度超限

      2.12.4 分支4：正常设置发送长度

      - 测试用例：有效参数，长度在有效范围内
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`u16TxLength = u16Length`
      - 测试结果：通过，正确设置发送数据长度

   2.13 HalWireComm_eClearRxComplete()

      2.13.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.13.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.13.3 分支3：正常清除接收完成标志

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`bRxComplete = FALSE_D`，`u16RxLength = 0U`
      - 测试结果：通过，正确清除接收完成标志和接收长度

   2.14 HalWireComm_eClearTxComplete()

      2.14.1 分支1：空指针检测

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.14.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态

      2.14.3 分支3：正常清除发送完成标志

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`bTxComplete = FALSE_D`
      - 测试结果：通过，正确清除发送完成标志

   2.15 HalWireComm_vRxCompleteCallback()

      2.15.1 分支1：空指针检测

      - 测试用例：传入 `pvUserData = NULL_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数安全处理空指针，转换为句柄指针后进行检查

      2.15.2 分支2：未初始化检测

      - 测试用例：句柄中`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确检测未初始化状态

      2.15.3 分支3：非接收状态

      - 测试用例：`eState != HALWIRECOMM_STATE_RX_E`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确检测非接收状态

      2.15.4 分支4：接收长度为零

      - 测试用例：处于接收状态，但DMA计算得到的接收长度为0
      - 预期结果：函数直接返回，不设置接收完成标志
      - 测试结果：通过，函数正确处理无数据接收情况
   
      2.15.5 分支5：正常接收完成处理（无回调）

      - 测试用例：处于接收状态，有有效接收长度，但无回调函数
      - 预期结果：设置接收完成标志，更新接收长度，状态切换到空闲
      - 测试结果：通过，正确处理接收完成，状态和标志正确更新
   
      2.15.6 分支6：正常接收完成处理（有回调）

      - 测试用例：处于接收状态，有有效接收长度，且有回调函数
      - 预期结果：设置接收完成标志，更新接收长度，状态切换到空闲，调用回调函数
      - 测试结果：通过，正确处理接收完成并调用回调函数
   
   2.16 HalWireComm_vTxCompleteCallback()

      2.16.1 分支1：空指针检测

      - 测试用例：传入 `pvUserData = NULL_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数安全处理空指针，转换为句柄指针后进行检查
   
      2.16.2 分支2：未初始化检测

      - 测试用例：句柄中`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确检测未初始化状态
   
      2.16.3 分支3：非发送状态

      - 测试用例：`eState != HALWIRECOMM_STATE_TX_E`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确检测非发送状态
   
      2.16.4 分支4：正常发送完成处理（无回调）

      - 测试用例：处于发送状态，但无回调函数
      - 预期结果：设置发送完成标志，状态切换到空闲
      - 测试结果：通过，正确处理发送完成，状态和标志正确更新

      2.16.5 分支5：正常发送完成处理（有回调）
   
      - 测试用例：处于发送状态，且有回调函数
      - 预期结果：设置发送完成标志，状态切换到空闲，调用回调函数
      - 测试结果：通过，正确处理发送完成并调用回调函数

   2.17 HalWireComm_vErrorOccurCallback()
   
      2.17.1 分支1：空指针检测

      - 测试用例：传入 `pvUserData = NULL_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数安全处理空指针，转换为句柄指针后进行检查
   
      2.17.2 分支2：未初始化检测

      - 测试用例：句柄中`bInitialized = FALSE_D`
      - 预期结果：函数直接返回，不执行任何操作
      - 测试结果：通过，函数正确检测未初始化状态
   
      2.17.3 分支3：正常错误处理（无回调）

      - 测试用例：已初始化状态，但无错误回调函数
      - 预期结果：状态设置为错误状态
      - 测试结果：通过，正确处理错误状态，状态正确更新
   
      2.17.4 分支4：正常错误处理（有回调）

      - 测试用例：已初始化状态，且有错误回调函数
      - 预期结果：状态设置为错误状态，调用错误回调函数
      - 测试结果：通过，正确处理错误状态并调用回调函数
   
   2.18 HalWireComm_eGetRxBuffer()

      2.18.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态
   
      2.18.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态
   
      2.18.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `ppu8RxBuffer = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态
   
      2.18.4 分支4：正常获取接收缓冲区指针

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`*ppu8RxBuffer` 指向接收缓冲区
      - 测试结果：通过，正确返回接收缓冲区指针，用户可读取接收到的数据
   
   2.19 HalWireComm_eGetTxBuffer()

      2.19.1 分支1：空指针检测（句柄）

      - 测试用例：传入 `psHandle = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.19.2 分支2：未初始化检测

      - 测试用例：`bInitialized = FALSE_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确检测未初始化状态
   
      2.19.3 分支3：空指针检测（输出参数）

      - 测试用例：传入 `ppu8TxBuffer = NULL_D`
      - 预期结果：返回 `HALWIRECOMM_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态
   
      2.19.4 分支4：正常获取发送缓冲区指针

      - 测试用例：有效参数，已初始化状态
      - 预期结果：返回 `HALWIRECOMM_OK_E`，`*ppu8TxBuffer` 指向发送缓冲区
      - 测试结果：通过，正确返回发送缓冲区指针，用户可写入待发送的数据
   
3. 集成测试详细结果

   3.1 RS485回显功能集成测试

      3.1.1 测试目标

      验证HalWireComm模块在实际硬件环境下的RS485通信功能，通过回显测试确保数据收发的完整性和可靠性。该测试验证模块在真实场景中的使用，包括硬件交互、中断处理、DMA传输等完整功能。

      3.1.2 测试环境

      - 通信接口：USART3配置为115200波特率，8数据位，1停止位，无校验
      - RS485收发器：连接到PB2引脚进行方向控制
      - DMA配置：DMA1_Stream1用于USART3_RX，DMA1_Stream3用于USART3_TX
      - 测试工具：PC端串口调试助手
   
      3.1.3 测试实现
   
      测试程序如下：
   
      ```c
      void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
      {
        UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_IDLE_E, huart);
      }
      
      void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
      {
        UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_TX_COMPLETE_E, huart);
      }
      
      void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
      {
        UartCallbackManager_vExecuteCallbacks(UART_CALLBACK_ERROR_E, huart);
      }
      
      void StartWireCommTask(void const * argument)
      {
        /* USER CODE BEGIN StartWireCommTask */
        /* 注册回调函数到UartCallbackManager */
        UartCallbackManager_vAddIdleCallback(&huart3, HalWireComm_vRxCompleteCallback, &sHalWireCommHandle);
        UartCallbackManager_vAddTxCompleteCallback(&huart3, HalWireComm_vTxCompleteCallback, &sHalWireCommHandle);
        UartCallbackManager_vAddErrorCallback(&huart3, HalWireComm_vErrorOccurCallback, &sHalWireCommHandle);
      
        HALWIRECOMM_CONFIG_T sConfig;
        HALWIRECOMM_STATUS_E eStatus;
        BOOL bRxComplete = FALSE_D;
        BOOL bTxComplete = FALSE_D;
        U16 u16RxLength = 0U;
        U8* pu8RxBuffer = NULL_D;
        U8* pu8TxBuffer = NULL_D;
      
        /* 获取默认配置 */
        eStatus = HalWireComm_eGetDefaultConfig(&sConfig);
        /* 初始化HalWireComm模块 */
        eStatus = HalWireComm_eInit(&sHalWireCommHandle, &sConfig);
        /* 获取缓冲区指针 */
        HalWireComm_eGetRxBuffer(&sHalWireCommHandle, &pu8RxBuffer);
        HalWireComm_eGetTxBuffer(&sHalWireCommHandle, &pu8TxBuffer);
        /* 启动接收模式 */
        eStatus = HalWireComm_eReceive(&sHalWireCommHandle);
      
        /* Infinite loop */
        for(;;)
        {
      	/* 检查是否有数据接收完成 */
      	HalWireComm_eIsRxComplete(&sHalWireCommHandle, &bRxComplete);
      	if (bRxComplete == TRUE_D)
      	{
      	  /* 获取接收到的数据长度 */
      	  HalWireComm_eGetRxLength(&sHalWireCommHandle, &u16RxLength);
      	  if (u16RxLength > 0U)
      	  {
      		/* 将接收到的数据复制到发送缓冲区 */
      		memcpy(pu8TxBuffer, pu8RxBuffer, u16RxLength);
      		/* 设置发送长度 */
      		HalWireComm_eSetTxLength(&sHalWireCommHandle, u16RxLength);
      		/* 清除接收完成标志 */
      		HalWireComm_eClearRxComplete(&sHalWireCommHandle);
      		/* 发送数据（回显） */
      		eStatus = HalWireComm_eTransmit(&sHalWireCommHandle);
      		/* 等待发送完成 */
      		do
      		{
      		  HalWireComm_eIsTxComplete(&sHalWireCommHandle, &bTxComplete);
      		  osDelay(1);
      		} while (bTxComplete == FALSE_D);
      
      		/* 清除发送完成标志 */
      		HalWireComm_eClearTxComplete(&sHalWireCommHandle);
      		/* 重新启动接收模式 */
      		HalWireComm_eReceive(&sHalWireCommHandle);
      	  }
      	  else
      	  {
      		/* 接收长度为0，清除标志并重新启动接收 */
      		HalWireComm_eClearRxComplete(&sHalWireCommHandle);
      		HalWireComm_eReceive(&sHalWireCommHandle);
      	  }
      	}
      	osDelay(10);
        }
        /* USER CODE END StartWireCommTask */
      }
      ```
   
      3.1.4 测试步骤
   
      - 通过串口调试助手发送各种测试数据，观察是否能收到相同的回显数据。
   
        通过串口调试助手向开发板发送数据，触发回调函数链表中对于接收数据回调函数的执行：
        
        ![测试图](Image/回调函数列表触发执行.png)
        
        ![测试图](Image/触发接收数据回调执行.png)
        
        执行接收数据回调函数之后，断点状态如下：
        
        ![测试图](Image/数据接收完成状态1.png)
        
        ![测试图](Image/数据接收完成状态2.png)
        
        测试程序向串口调试助手发送回显数据，触发回调函数链表中对于发送数据回调函数的执行：
        
        ![测试图](Image/触发发送数据回调执行.png)
        
        执行发送数据回调函数之后，断点状态如下：
        
        ![测试图](Image/数据发送完成状态1.png)
        
        ![测试图](Image/数据发送完成状态2.png)
        
        整体测试效果如下：
        
        ![测试图](Image/串口调试工具回显测试.png)
   
   
   测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

无

### 可重用性声明

1. 该模块依赖于STM32 HAL库和GlobalTypes类型定义。
2. 该模块依赖于Utility模块提供的微秒级延时功能。
3. 该模块需要正确配置的UART外设和DMA通道。
4. 该模块需要正确配置的RS485方向控制GPIO引脚。
