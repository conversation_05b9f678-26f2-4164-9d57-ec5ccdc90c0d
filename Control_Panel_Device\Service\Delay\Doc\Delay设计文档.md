- [Delay文件](#Delay文件)
  - [描述](#描述)
  - [要求](#要求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [配置 Id(s)](#配置-ids)
    - [参数 Id(s)](#参数-ids)
    - [过程数据 Id(s)](#过程数据-ids)
    - [变频器属性 Id(s)](#变频器属性-ids)
    - [命令 Id(s)](#命令-ids)
    - [事件 Id(s)](#事件-ids)
  - [设计](#设计)
    - [理论依据](#理论依据)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [单元测试](#单元测试)
    - [静态分析](#静态分析)
    - [工程测试](#工程测试)
  - [设计的局限性](#设计的局限性)
    - [已知 Bugs](#已知-bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)


&nbsp;

***

## Delay文件


### 描述

`Delay`模块基于ARM Cortex-M内核的DWT（Data Watchpoint and Trace）外设，提供高精度的微秒级和毫秒级延时功能。该模块适用于需要精确控制短时间延迟的场景。

### 要求

#### 产品需求

| 产品需求文档 ID | 产品需求文档 标题  |
|----------------|-------------------|
|     无         |                   |

#### 软件需求

  - 应提供微秒(us)和毫秒(ms)级别的延时功能。
  - 延时功能必须是高精度的。
  - 应提供初始化和反初始化接口。
  - 应提供获取当前计数器值和计算时间差的接口。
  - 实现中必须考虑DWT计数器溢出的情况。


#### 假设

- DWT外设可用，且系统中没有其他模块冲突使用。
- 系统核心时钟频率在运行期间是稳定不变的。

### 平台资源

 接口是组件定义系统功能的提供和使用的"契约"接口。

#### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| core_cm4.h      | 访问ARM Cortex-M4内核寄存器，如`DWT`和`CoreDebug`。 |


#### 提供软件接口

| 接口名称 | 目的 |
|---------|------|
| `Delay_bInit(u32SystemCoreClock: uint32_t): bool` | 初始化DWT延时模块，配置系统时钟频率 |
| `Delay_vDeInit(void): void` | 反初始化DWT延时模块，释放资源 |
| `Delay_vDelayUs(u32MicroSeconds: uint32_t): void` | 微秒级精确延时，支持1μs到1,000,000μs |
| `Delay_vDelayMs(u32MilliSeconds: uint32_t): void` | 毫秒级延时，支持1ms到1,000ms |
| `Delay_u32GetCounter(void): uint32_t` | 获取当前DWT计数器值，用于时间测量 |
| `Delay_u32GetTimeDiffUs(u32StartCounter: uint32_t, u32EndCounter: uint32_t): uint32_t` | 计算两个计数器值之间的时间差（微秒），自动处理溢出 |

#### 配置 Id(s)

无

#### 参数 Id(s)

无

#### 过程数据 Id(s)

无

#### 变频器属性 Id(s)

无

#### 命令 Id(s)

无

#### 事件 Id(s)

无

### 设计

#### 理论依据
本设计利用了ARM Cortex-M4内核中的DWT（Data Watchpoint and Trace）单元。DWT内含一个32位的自由运行周期计数器`CYCCNT`，该计数器在每个处理器时钟周期递增。通过读取该计数器的值，可以实现高分辨率、低开销的精确定时测量和延时功能。本模块的核心便是围绕`CYCCNT`寄存器进行操作，以提供精确的延时服务。

#### 静态设计
Delay模块采用模块化设计，主要由以下几个部分组成：

![](Image/Delay_class.png)

**核心模块**：
- **Delay模块**：主模块，提供所有对外接口，内部维护状态信息
- **私有状态变量**：
  - `Delay_u32CyclesPerUs`：每微秒对应的CPU时钟周期数，根据系统时钟频率计算得出
  - `Delay_bInitialized`：初始化状态标志

**硬件依赖**：

- **ARM Cortex-M Core**：依赖ARM Cortex-M内核的DWT（Data Watchpoint and Trace）外设
- **DWT->CYCCNT**：32位自由运行周期计数器，是实现精确延时的核心
- **CoreDebug->DEMCR**：用于启用trace和debug功能

#### 动态设计

模块的主要动态行为包括初始化流程、延时执行流程和时间差计算流程：

![](Image/Delay_flow.png)

**初始化流程（Delay_bInit）**：
1. 检查输入参数有效性（系统时钟频率不能为0）
2. 检查是否已经初始化，如果是则直接返回成功
3. 根据系统时钟频率计算每微秒的周期数
4. 验证计算结果的有效性
5. 启用DWT计数器
6. 设置初始化标志并返回成功

**微秒延时流程（Delay_vDelayUs）**：
1. 检查模块是否已初始化和延时参数有效性
2. 检查延时时间是否超过最大限制，如超过则限制到最大值
3. 计算所需的时钟周期数
4. 获取起始时钟周期值
5. 进入忙等待循环，持续检查已经过的周期数
6. 当已经过的周期数达到目标值时退出

### 测试

#### 单元测试
 底层模块，无需单元测试。

#### 静态分析

暂无

#### 工程测试
  通过以下步骤进行工程测试：
  1. 搭建测试工程，在主循环前调用`Delay_bInit()`进行初始化。
  2. 使用一个GPIO引脚来验证延时准确性。在循环中，通过示波器观察GPIO引脚的波形。
  3. 测试`Delay_vDelayUs()`：
     - 在循环中配置GPIO引脚翻转，引脚拉高后调用`Delay_vDelayUs(100)`，然后引脚拉低再调用`Delay_vDelayUs(100)`。
     - 使用示波器测量GPIO引脚的波形周期，应为200us。
     - 更换不同的延时参数进行测试。
  4. 测试`Delay_vDelayMs()`：
     - 采用类似方法，调用`Delay_vDelayMs(10)`，预期波形周期为20ms。
  5. 测试`Delay_u32GetTimeDiffUs()`：
     - 获取起始计数值，调用一个已知时长的函数（如`Delay_vDelayUs(50)`），再获取结束计数值。
     - 调用`Delay_u32GetTimeDiffUs()`计算时间差，验证结果是否约等于50us。

### 设计的局限性

#### 已知 Bugs

无

#### 未来的改进

- **毫秒延时优化**: 当前的`Delay_vDelayMs`是通过循环调用`Delay_vDelayUs(1000)`实现的。对于较长的延时，循环本身会引入微小的额外开销。未来可优化为直接计算毫秒对应的总周期数进行一次性延时，但需注意处理32位计数器可能溢出的情况。
- **RTOS环境下的问题**: 本模块采用忙等待（busy-wait）实现延时，在多任务抢占式RTOS环境下，如果一个任务在执行延时期间被更高优先级的任务抢占，实际延时时间将长于设定时间。因此，本模块不适用于需要阻塞任务以让出CPU的场景，仅适用于短时间的、不希望发生任务切换的精确延时。

#### 可重用性声明
- 本模块可在任何包含DWT外设的ARM Cortex-M3/M4/M7平台上通用。
- 模块依赖于CMSIS核心访问层头文件（如`core_cm4.h`）。 