<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>5</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>100</x>
      <y>0</y>
      <w>250</w>
      <h>780</h>
    </coordinates>
    <panel_attributes>HalUi Init
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>190</x>
      <y>25</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>160</x>
      <y>50</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>125</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check SPI handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>165</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>200</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check GPIO ports</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>240</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>275</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check GPIO pins</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>315</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>350</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check resolution
HRes != 0 &amp;&amp; VRes != 0</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>390</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>425</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check buffer divisor
1 &lt;= divisor &lt;= 100</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>465</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>500</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Save configuration
Copy to handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>540</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Initialize handle
Set default values</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>580</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Init Lcd IO</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>620</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>655</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set bInitialized = TRUE</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>695</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>190</x>
      <y>750</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>255</x>
      <y>695</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>30</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>70</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>145</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>180</y>
      <w>70</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid SPI handle]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>220</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>255</y>
      <w>70</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid GPIO ports]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>295</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>330</y>
      <w>70</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid GPIO pins]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>370</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>405</y>
      <w>65</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid resolution]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>445</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>480</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid divisor]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>520</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>200</x>
      <y>165</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Invalid SPI handle]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>200</x>
      <y>240</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Invalid GPIO ports]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>200</x>
      <y>315</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Invalid GPIO pins]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>200</x>
      <y>390</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Invalid resolution]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>200</x>
      <y>465</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Invalid divisor]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>290</x>
      <y>95</y>
      <w>15</w>
      <h>610</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;1200.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>385</x>
      <y>0</y>
      <w>260</w>
      <h>555</h>
    </coordinates>
    <panel_attributes>HalUi DeInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>490</x>
      <y>25</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>455</x>
      <y>50</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>485</x>
      <y>90</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>455</x>
      <y>200</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Free draw buffer 1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>455</x>
      <y>340</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Free draw buffer 2</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>455</x>
      <y>400</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Clear handle
Reset all values</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>445</x>
      <y>445</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set bInitialized = FALSE</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>490</x>
      <y>505</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>30</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>70</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>105</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>360</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>420</y>
      <w>15</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>465</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>95</y>
      <w>95</w>
      <h>400</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;780.0;170.0;780.0;170.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>560</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>600</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>635</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Init success]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>675</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>710</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>200</x>
      <y>620</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Init failed]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>290</x>
      <y>710</y>
      <w>15</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>680</x>
      <y>0</y>
      <w>275</w>
      <h>390</h>
    </coordinates>
    <panel_attributes>HalUi GetDefaultConfig
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>790</x>
      <y>20</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>760</x>
      <y>45</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>785</x>
      <y>85</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>755</x>
      <y>120</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set default SPI handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>755</x>
      <y>160</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set default GPIO pins
CS, DCX, RESET</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>755</x>
      <y>200</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set default GPIO ports
CS, DCX, RESET</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>705</x>
      <y>240</y>
      <w>180</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set default display config
resolution, invert, antialiasing, rotaion, bufferdivisor</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>755</x>
      <y>280</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>860</x>
      <y>280</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>790</x>
      <y>335</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>25</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>65</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>100</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>140</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>180</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>220</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>260</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>295</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>85</y>
      <w>110</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>10.0;20.0;200.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>895</x>
      <y>90</y>
      <w>15</w>
      <h>200</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;380.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>295</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;220.0;40.0;220.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>100</x>
      <y>805</y>
      <w>255</w>
      <h>420</h>
    </coordinates>
    <panel_attributes>HalUi LcdIoInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>825</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>150</x>
      <y>850</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Register SPI callback</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>180</x>
      <y>890</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>150</x>
      <y>925</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Reset LCD low</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>150</x>
      <y>965</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Delay reset timing</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>150</x>
      <y>1005</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Reset LCD high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>150</x>
      <y>1045</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Delay reset timing</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>150</x>
      <y>1085</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set CS and DCX high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>150</x>
      <y>1125</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>250</x>
      <y>1125</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>1175</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>830</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>870</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>905</y>
      <w>70</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Register success]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>945</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>985</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>1025</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>1065</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>1105</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>1140</y>
      <w>15</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>890</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Register failed]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>285</x>
      <y>895</y>
      <w>15</w>
      <h>240</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;460.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>185</x>
      <y>1140</y>
      <w>115</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;210.0;40.0;210.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>385</x>
      <y>805</y>
      <w>340</w>
      <h>785</h>
    </coordinates>
    <panel_attributes>HalUi CreateLcdDisplay
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>505</x>
      <y>825</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>850</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>500</x>
      <y>890</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>925</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check display pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>500</x>
      <y>965</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1000</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Create ST7789 display</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>500</x>
      <y>1040</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1075</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set rotation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1115</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set color invert</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1155</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set antialiasing</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>460</x>
      <y>1195</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Calculate draw buffer size</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1235</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Allocate draw buffer 1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>500</x>
      <y>1275</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1310</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Allocate draw buffer 2</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>500</x>
      <y>1350</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1385</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set display buffers
lv_display_set_buffers()</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>470</x>
      <y>1445</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>620</x>
      <y>1445</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>505</x>
      <y>1495</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>830</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>870</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>905</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>945</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>980</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1020</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1055</y>
      <w>65</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Create success]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1095</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1135</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1175</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1215</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1255</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1290</y>
      <w>70</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Allocate success]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1330</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1365</y>
      <w>70</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Allocate success]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1405</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1460</y>
      <w>15</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>515</x>
      <y>890</y>
      <w>155</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;290.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>410</x>
      <y>965</y>
      <w>100</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not null pointer]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>515</x>
      <y>1040</y>
      <w>155</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Create failed]</panel_attributes>
    <additional_attributes>10.0;20.0;290.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>515</x>
      <y>1275</y>
      <w>155</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Allocate failed]</panel_attributes>
    <additional_attributes>10.0;20.0;290.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>515</x>
      <y>1350</y>
      <w>95</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
[Allocate failed]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0;170.0;70.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>655</x>
      <y>895</y>
      <w>15</w>
      <h>560</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;1100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>1460</y>
      <w>165</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;310.0;40.0;310.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>200</x>
      <y>90</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>185</x>
      <y>90</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>105</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>710</y>
      <w>115</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;210.0;40.0;210.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>445</x>
      <y>125</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check draw buffer 1 pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>145</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>485</x>
      <y>165</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>415</x>
      <y>170</y>
      <w>90</w>
      <h>80</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;10.0;10.0;140.0;160.0;140.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>180</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>280</y>
      <w>15</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>485</x>
      <y>305</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>415</x>
      <y>305</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>140.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>320</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>500</x>
      <y>90</y>
      <w>85</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;150.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>445</x>
      <y>260</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check draw buffer 2 pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>220</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>415</x>
      <y>165</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>10.0;20.0;140.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>415</x>
      <y>310</y>
      <w>90</w>
      <h>80</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;10.0;10.0;140.0;160.0;140.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>410</x>
      <y>970</y>
      <w>110</w>
      <h>465</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>200.0;910.0;10.0;910.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>560</x>
      <y>1385</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Free draw buffer 1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>595</x>
      <y>1405</y>
      <w>75</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>130.0;40.0;10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>760</x>
      <y>805</y>
      <w>250</w>
      <h>350</h>
    </coordinates>
    <panel_attributes>HalUi IsLcdBusy
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>865</x>
      <y>830</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>835</x>
      <y>855</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>860</x>
      <y>895</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>825</x>
      <y>930</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>860</x>
      <y>970</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>835</x>
      <y>1005</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set busy status</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>835</x>
      <y>1045</y>
      <w>70</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>925</x>
      <y>1045</y>
      <w>70</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>865</x>
      <y>1100</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>835</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>875</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>910</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>950</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>985</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>1025</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>1060</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>875</x>
      <y>895</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>875</x>
      <y>970</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>955</x>
      <y>900</y>
      <w>15</w>
      <h>155</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;290.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>865</x>
      <y>1060</y>
      <w>105</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;190.0;40.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>85</x>
      <y>2350</y>
      <w>290</w>
      <h>500</h>
    </coordinates>
    <panel_attributes>HalUi WaitLcdReady
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>205</x>
      <y>2375</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>2400</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>200</x>
      <y>2440</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>2475</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Get start tick</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>160</x>
      <y>2520</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Convert timeout time to ticks</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>160</x>
      <y>2580</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check LCD bus state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>200</x>
      <y>2620</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>2655</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check timeout</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>200</x>
      <y>2695</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>230</x>
      <y>2720</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>delay for wait LCD</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>95</x>
      <y>2765</y>
      <w>75</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>290</x>
      <y>2765</y>
      <w>70</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>175</x>
      <y>2765</y>
      <w>70</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_TIMEOUT_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>205</x>
      <y>2820</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2380</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2420</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2455</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2495</y>
      <w>15</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2540</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2600</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2635</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Bus busy]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>215</x>
      <y>2695</y>
      <w>60</w>
      <h>35</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[No timeout]</panel_attributes>
    <additional_attributes>100.0;50.0;100.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>215</x>
      <y>2440</y>
      <w>120</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2710</y>
      <w>75</w>
      <h>65</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
[Timeout occurred]</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;110.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>320</x>
      <y>2445</y>
      <w>15</w>
      <h>330</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;640.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>390</x>
      <y>2350</y>
      <w>280</w>
      <h>350</h>
    </coordinates>
    <panel_attributes>HalUi GetLcdDisplay
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>495</x>
      <y>2375</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>460</x>
      <y>2400</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>490</x>
      <y>2440</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>460</x>
      <y>2475</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>490</x>
      <y>2515</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>460</x>
      <y>2550</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set display pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>465</x>
      <y>2590</y>
      <w>70</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>555</x>
      <y>2590</y>
      <w>75</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return 
HALUI_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>495</x>
      <y>2645</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2380</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2420</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2455</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2495</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2530</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2570</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2605</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>2440</y>
      <w>100</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>505</x>
      <y>2515</y>
      <w>100</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>590</x>
      <y>2445</y>
      <w>15</w>
      <h>155</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;290.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>2605</y>
      <w>110</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;200.0;40.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>705</x>
      <y>2350</y>
      <w>250</w>
      <h>400</h>
    </coordinates>
    <panel_attributes>HalUi LcdColorTransferReadyCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>800</x>
      <y>2375</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>2400</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>795</x>
      <y>2440</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>2475</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set CS pin high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>760</x>
      <y>2515</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set LCD bus state idle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>765</x>
      <y>2555</y>
      <w>80</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check display pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>795</x>
      <y>2595</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>2630</y>
      <w>105</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Notify LVGL transfer complete</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>800</x>
      <y>2690</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2380</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2420</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2455</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2495</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2535</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2575</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2610</y>
      <w>55</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2650</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>810</x>
      <y>2440</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>810</x>
      <y>2595</y>
      <w>105</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>10.0;20.0;190.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>2445</y>
      <w>115</w>
      <h>235</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;450.0;210.0;450.0;210.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>90</x>
      <y>1625</y>
      <w>250</w>
      <h>665</h>
    </coordinates>
    <panel_attributes>HalUi LcdSendCommand
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>195</x>
      <y>1650</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>1675</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>190</x>
      <y>1715</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>1750</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Wait for LCD bus idle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>1790</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set SPI 8-bit mode</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>1830</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Initialize SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>155</x>
      <y>1870</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set DCX low (command)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>1910</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set CS low</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>1950</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Send command</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>190</x>
      <y>1990</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>2025</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set DCX high (data)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>2065</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Send parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>165</x>
      <y>2105</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set CS high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>195</x>
      <y>2165</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1655</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1695</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1730</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1770</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1810</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1850</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1890</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1930</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1970</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>2005</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[HAL_OK]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>2045</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>2085</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>2125</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>1715</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>1990</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[HAL_ERROR]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>195</x>
      <y>1720</y>
      <w>105</w>
      <h>435</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;850.0;190.0;850.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>375</x>
      <y>1625</y>
      <w>260</w>
      <h>665</h>
    </coordinates>
    <panel_attributes>HalUi LcdSendColor
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>480</x>
      <y>1650</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>440</x>
      <y>1675</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>475</x>
      <y>1715</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>440</x>
      <y>1750</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Wait for LCD bus idle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>1790</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set SPI 8-bit mode</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>1830</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Initialize SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>435</x>
      <y>1870</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set DCX low (command)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>1910</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set CS low</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>1950</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Send command</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>475</x>
      <y>1990</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>2025</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set DCX high (data)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>2065</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set SPI 16-bit mode</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>2105</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Initialize SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>435</x>
      <y>2145</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set LCD bus state busy</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>435</x>
      <y>2185</y>
      <w>100</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Send color data DMA</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>480</x>
      <y>2245</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1655</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1695</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1730</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1770</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1810</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1850</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1890</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1930</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1970</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2005</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[HAL_OK]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2045</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2085</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2125</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2165</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2205</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>1715</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>490</x>
      <y>1990</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[HAL_ERROR]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>1720</y>
      <w>105</w>
      <h>515</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;1010.0;190.0;1010.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>670</x>
      <y>1625</y>
      <w>285</w>
      <h>690</h>
    </coordinates>
    <panel_attributes>HalUi LcdSendCommand
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1655</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>780</x>
      <y>1650</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>740</x>
      <y>1675</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Check handle initialized</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1695</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>775</x>
      <y>1715</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>1715</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Not initialized]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1730</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Initialized]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>740</x>
      <y>1750</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Wait for LCD bus idle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1770</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>1790</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set SPI 8-bit mode</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1810</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>1830</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Initialize SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1850</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>740</x>
      <y>1870</y>
      <w>90</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set DCX low (command)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1890</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>1910</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set CS low</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1930</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>1950</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Send command</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1970</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>775</x>
      <y>1990</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>2005</y>
      <w>45</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[HAL_OK]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>2025</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set DCX high (data)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>2045</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>2065</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Send parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>2085</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>2105</y>
      <w>70</w>
      <h>25</h>
    </coordinates>
    <panel_attributes>Set CS high</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>2125</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>780</x>
      <y>1720</y>
      <w>105</w>
      <h>435</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;850.0;190.0;850.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>790</x>
      <y>1990</y>
      <w>95</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[HAL_ERROR]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>780</x>
      <y>2165</y>
      <w>10</w>
      <h>10</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>125</x>
      <y>2620</y>
      <w>85</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>[Bus idle]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>125</x>
      <y>2625</y>
      <w>15</w>
      <h>150</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;280.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2675</y>
      <w>15</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2555</y>
      <w>145</w>
      <h>210</h>
    </coordinates>
    <panel_attributes>[No timeout]</panel_attributes>
    <additional_attributes>10.0;10.0;200.0;10.0;200.0;400.0;120.0;400.0;120.0;380.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2780</y>
      <w>15</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>205</x>
      <y>2780</y>
      <w>130</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;240.0;40.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>125</x>
      <y>2780</y>
      <w>95</w>
      <h>30</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>170.0;40.0;10.0;40.0;10.0;10.0</additional_attributes>
  </element>
</diagram>
