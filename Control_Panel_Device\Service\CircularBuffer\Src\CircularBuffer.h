/**
 * @remark 版权所有(C)2025 TRIED.
 * @remark 本软件受版权法保护，未经授权禁止复制、分发或修改。
 * @file CircularBuffer.h
 * @brief 循环缓冲区头文件
 * @details 提供单向循环缓冲区的接口定义，包括数据读写、状态查询等功能
 * <AUTHOR>
 * @date 2025-06-13
 * @note 适用于STM32F4系列微控制器
 */

#ifndef __CIRCULAR_BUFFER_H
#define __CIRCULAR_BUFFER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* Exported constants --------------------------------------------------------*/
#define CIRCULARBUFFER_DEFAULT_SIZE     (256U)
#define CIRCULARBUFFER_MAX_SIZE         (4096U)

/* Exported types ------------------------------------------------------------*/
typedef enum {
    CIRCULARBUFFER_OK = 0,
    CIRCULARBUFFER_ERROR,
    CIRCULARBUFFER_FULL,
    CIRCULARBUFFER_EMPTY,
    CIRCULARBUFFER_INVALID_PARAM
} CIRCULARBUFFER_STATUS_E;

typedef struct {
    uint8_t* pu8Buffer;
    uint16_t u16Size;
    volatile uint16_t u16Head;
    volatile uint16_t u16Tail;
    volatile uint16_t u16Count;
    bool bInitialized;
} CIRCULARBUFFER_T;

/* Exported functions prototypes ---------------------------------------------*/
/**
 * @brief 初始化单向循环缓冲区
 * @param psBuffer 缓冲区结构体指针
 * @param pu8BufferMem 缓冲区内存指针
 * @param u16Size 缓冲区大小
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eInit(CIRCULARBUFFER_T* psBuffer, uint8_t* pu8BufferMem, uint16_t u16Size);

/**
 * @brief 反初始化单向循环缓冲区
 * @param psBuffer 缓冲区结构体指针
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eDeInit(CIRCULARBUFFER_T* psBuffer);

/**
 * @brief 向循环缓冲区写入单个字节
 * @param psBuffer 缓冲区结构体指针
 * @param u8Data 要写入的数据
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_ePutByte(CIRCULARBUFFER_T* psBuffer, uint8_t u8Data);

/**
 * @brief 从循环缓冲区读取单个字节
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 读取数据的指针
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eGetByte(CIRCULARBUFFER_T* psBuffer, uint8_t* pu8Data);

/**
 * @brief 向循环缓冲区写入多个字节
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 要写入的数据指针
 * @param u16Length 数据长度
 * @retval uint16_t 实际写入的字节数
 */
uint16_t CircularBuffer_u16PutBytes(CIRCULARBUFFER_T* psBuffer, const uint8_t* pu8Data, uint16_t u16Length);

/**
 * @brief 从循环缓冲区读取多个字节
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 读取数据的指针
 * @param u16Length 要读取的数据长度
 * @retval uint16_t 实际读取的字节数
 */
uint16_t CircularBuffer_u16GetBytes(CIRCULARBUFFER_T* psBuffer, uint8_t* pu8Data, uint16_t u16Length);

/**
 * @brief 获取循环缓冲区中可用数据的长度
 * @param psBuffer 缓冲区结构体指针
 * @retval uint16_t 可用数据长度
 */
uint16_t CircularBuffer_u16GetCount(const CIRCULARBUFFER_T* psBuffer);

/**
 * @brief 获取循环缓冲区中剩余空间大小
 * @param psBuffer 缓冲区结构体指针
 * @retval uint16_t 剩余空间大小
 */
uint16_t CircularBuffer_u16GetFreeSpace(const CIRCULARBUFFER_T* psBuffer);

/**
 * @brief 检查循环缓冲区是否为空
 * @param psBuffer 缓冲区结构体指针
 * @retval bool true-空，false-非空
 */
bool CircularBuffer_bIsEmpty(const CIRCULARBUFFER_T* psBuffer);

/**
 * @brief 检查循环缓冲区是否已满
 * @param psBuffer 缓冲区结构体指针
 * @retval bool true-满，false-未满
 */
bool CircularBuffer_bIsFull(const CIRCULARBUFFER_T* psBuffer);

/**
 * @brief 清空循环缓冲区
 * @param psBuffer 缓冲区结构体指针
 * @retval CIRCULARBUFFER_STATUS_E 操作状态
 */
CIRCULARBUFFER_STATUS_E CircularBuffer_eClear(CIRCULARBUFFER_T* psBuffer);

/**
 * @brief 预览循环缓冲区数据（不从缓冲区中移除）
 * @param psBuffer 缓冲区结构体指针
 * @param pu8Data 预览数据的指针
 * @param u16Length 要预览的数据长度
 * @retval uint16_t 实际预览的字节数
 */
uint16_t CircularBuffer_u16Peek(const CIRCULARBUFFER_T* psBuffer, uint8_t* pu8Data, uint16_t u16Length);

#ifdef __cplusplus
}
#endif

#endif /* __CIRCULAR_BUFFER_H */ 